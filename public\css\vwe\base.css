/*
* Skeleton V1.2
* Copyright 2011, <PERSON>
* www.getskeleton.com
* Free to use under the MIT license.
* http://www.opensource.org/licenses/mit-license.php
* 6/20/2012
*/


/* Table of Content
==================================================
	#Reset & Basics
	#Basic Styles
	#Site Styles
	#Typography
	#Links
	#Lists
	#Images
	#Buttons
	#Forms
	#Misc */


/* #Reset & Basics (Inspired by <PERSON><PERSON>)
================================================== */
	html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video, select{
		margin: 0;
		padding: 0;
		border: 0;
		font-size: 100%;
		font: inherit;
		vertical-align: baseline; }
	article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
		display: block; }
	body {
		line-height: 1; }
	ol, ul {
		list-style: none; }
	blockquote, q {
		quotes: none; }
	blockquote:before, blockquote:after,
	q:before, q:after {
		content: '';
		content: none; }
	table {
		border-collapse: collapse;
		border-spacing: 0; }

/* #Basic Styles
================================================== */
body {
	background: #f2f2f2;
	font-family: 'Open Sans';
	font-weight:300;
	font-size: 15px;
	line-height:22px;
	/* color:#fff; */
	overflow-x:hidden;
	-webkit-font-smoothing: antialiased; /* Fix for webkit rendering */
	text-shadow: 1px 1px 1px rgba(0,0,0,0.004);
	-webkit-text-size-adjust: 100%;
 }

/* #Typography
================================================== */
h1, h2, h3, h4, h5, h6 {
	/* color: #dbdbdb; */
	font-family: 'Poppins', sans-serif;
	font-weight:400;
}
h1 a, h2 a, h3 a, h4 a, h5 a, h6 a { font-weight: inherit; }
h1 { font-size: 40px; line-height:48px;}
h2 { font-size: 34px; line-height:42px;}
h3 { font-size: 30px; line-height: 38px;}
h4 { font-size: 24px; line-height: 32px;}
h5 { font-size: 16px; line-height: 22px;font-weight:600;}
h6 { font-size: 14px; line-height: 20px;font-weight:600;}


	p img { margin: 0; }
	p.lead { font-size: 21px; line-height: 27px; color: #777;  }

	em { font-style: italic; }
	strong { font-weight: 600;}
	small { font-size: 80%; }



/*	Blockquotes  */
	blockquote, blockquote p { font-size: 17px; line-height: 24px; color: #777; }
	blockquote { margin: 0 0 20px; padding: 9px 20px 0 19px; }
	blockquote cite { display: block; font-size: 12px; color: #555; }
	blockquote cite:before { content: "\2014 \0020"; }
	blockquote cite a, blockquote cite a:visited, blockquote cite a:visited { color: #555; }

	hr { border: solid #323a3d; border-width: 1px 0 0; clear: both; margin:0 10px 0 10px; height: 0; position:relative;}


/* #Links
================================================== */
	a, a:visited { text-decoration: none; }
	a:hover, a:focus { color: #e74c3c;}
	p a, p a:visited { line-height: inherit; text-decoration: none;}
	a:focus{
	outline:none;
	}

/* #Lists
================================================== */
	ul, ol { }
	ul { list-style: none outside; }
	ol { list-style: decimal; }
	ol, ul.square, ul.circle, ul.disc { margin-left: 30px; }
	ul.square { list-style: square outside; }
	ul.circle { list-style: circle outside; }
	ul.disc { list-style: disc outside; }
	ul ul, ul ol,
	ol ol, ol ul { font-size: 90%;  }
	ul ul li, ul ol li,
	ol ol li, ol ul li {  }
	li { line-height: 18px; }
	ul.large li { line-height: 21px; }
	li p { line-height: 21px; }
/* #Buttons
================================================== */

	/* .button,
	button,
	input[type="submit"],
	input[type="reset"],
	input[type="button"] {
	background:#363636;
	margin-top:10px;
	border:none;
	  color: #dfdfdf;
	  display: inline-block;
	  font-size: 16px;
	  cursor: pointer;
	  line-height: normal;
	  padding: 15px 25px;
	-webkit-transition : all 0.3s ease-out;
	-moz-transition : all 0.3s ease-out;
	-o-transition :all 0.3s ease-out;
	transition : all 0.3s ease-out;
	width:100%}

	.button:hover,
	button:hover,
	input[type="submit"]:hover,
	input[type="reset"]:hover,
	input[type="button"]:hover {
	  color: #e74c3c;
	background:#292929;	 }

	.button:active,
	button:active,
	input[type="submit"]:active,
	input[type="reset"]:active,
	input[type="button"]:active {
	  color: #e74c3c;}

	.button.full-width,
	button.full-width,
	input[type="submit"].full-width,
	input[type="reset"].full-width,
	input[type="button"].full-width {
		width: 100%;
		padding-left: 0 !important;
		padding-right: 0 !important;
		text-align: center; }

	button::-moz-focus-inner,
	input::-moz-focus-inner {
    border: 0;
    padding: 0;
	} */
/* #Images
================================================== */






/* #Misc
================================================== */
	.remove-bottom { margin-bottom: 0 !important; }
	.half-bottom { margin-bottom: 12.5px !important; }
	.add-bottom { margin-bottom: 25px !important; }
	.remove-top { margin-top: 0 !important; }
	.half-top { margin-top: 12.5px !important; }
	.add-top { margin-top: 25px !important; }

.clear {
	clear: both;
	display: block;
	overflow: hidden;
	visibility: hidden;
	width: 0;
	height: 0;
}
::selection {
	color:#fff;
}
::-moz-selection {
	color:#fff;
}
