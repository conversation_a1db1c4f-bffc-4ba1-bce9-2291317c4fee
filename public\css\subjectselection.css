.dark-title-section {
    background-color: #000;
    color: #fff;
}

.border-tb-heading {
    border: 1px solid #000;
    border-right: none;
    border-left: none;
    margin: 0 -15px;
    padding: 15px 30px;
    font-size: 26px;
    font-family: '<PERSON>', sans-serif;
    letter-spacing: 4px;
    color: #000;
}

.border-tb-heading>input {
    width: 100%;
    border: none;
    text-align: center;
    font-size: 26px;
    font-family: '<PERSON>', sans-serif;
    letter-spacing: 4px;
    color: #000;
    text-transform: uppercase;
}

.ss-tiles {
    display: flex;
    display: -webkit-flex;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    margin: 0 -15px;
}

.ss-tiles>li {
    padding: 0 !important;
    margin: 15px;
    position: relative;
    color: #000;
}

.ss-tiles:not(.ss-tiles-input)>li {
    border: 1px solid #000;
}

.ss-tiles-input input[type=checkbox],
.ss-tiles-input input[type=radio] {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.ss-tiles-input input[type=checkbox]+label,
.ss-tiles-input input[type=radio]+label {
    border: 1px solid #000;
    margin-bottom: 0;
    cursor: pointer;
}

.ss-tiles-input input[type=checkbox]:not(:checked)+label,
.ss-tiles-input input[type=radio]:not(:checked)+label {
    opacity: 0.4;
}

.ss-tiles>li>a {
    color: #000;
}

.ss-tile-top-left {
    position: absolute;
    left: 5px;
}

.ss-tile-top-right {
    position: absolute;
    right: 5px;
}

.ss-color-box {
    padding: 20px 25px;
    border-bottom: 1px solid #000;
}

.ss-color-box>.ss-bordered-box {
    border: 1px solid #000;
}

.ss-color-box>.ss-bordered-box>div {
    outline: 1px solid #000;
}

.ss-color-box>div {
    text-align: center;
    height: 170px;
    width: 300px;
    position: relative;
}

.ss-tile-inner-bg {
    position: absolute;
    height: 100%;
    width: 100%;
}

.color-pick {
    flex: 1;
    -ms-flex: 1;
}

.ss-tile-overlay {
    color: #000;
    font-size: 30px;
    line-height: 30px;
    text-transform: uppercase;
}

.ss-tile-footer {
    padding: 15px;
    width: 350px;
}

.ellipsify {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: 90%;
    display: inline-block;
}

.outline-input {
    width: 100%;
    border: 1px solid #000;
    padding: 8px;
    margin-bottom: 10px;
}

.input-outlined {
    border: 2px solid #000;
}

.select2-search--inline {
    display: contents;
    /*this will make the container disappear, making the child the one who sets the width of the element*/
}

.select2-search__field:placeholder-shown {
    width: 100% !important;
    /*makes the placeholder to be 100% of the width while there are no options selected*/
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #000 transparent transparent transparent;
}

.select2-container {
    background-color: #fff;
}

.select2-selection--multiple:after {
    content: "";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    border-top: 5px solid #333;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
}

.modal-subject-view,
.modal-subject-view .modal-header,
.modal-subject-view p,
.modal.modal-subject-send,
.modal.modal-subject-send p {
    color: #000;
}

.modal.modal-subject-view .modal-header {
    padding-top: 30px;
}

.modal.modal-subject-view .modal-content,
.modal.modal-subject-send .modal-content {
    border: 2px solid #000;
    border-radius: 0;
}

.modal-subject-view .border-tb-heading {
    padding: 15px 25px;
    margin: 0 -25px;
    font-size: 18px;
    font-family: 'Roboto', sans-serif;
    letter-spacing: 2px;
}

.modal.modal-subject-send .modal-header {
    background-color: #000;
    color: #fff;
    font-family: 'Roboto', sans-serif;
    font-size: 18px;
    letter-spacing: 2px;
    text-transform: uppercase;
    padding: 15px;
}

.unit-input {
    font-family: 'Roboto';
    font-size: 36px;
    border: none;
    max-width: 100%;
    width: 400px;
    margin-top: 50px;
}

.subject-outline-input {
    font-family: 'Roboto';
    font-size: 26px;
    border: none;
    max-width: 100%;
    width: 800px;
    margin-top: 50px;
}

@media (min-width: 992px) {
    .modal.modal-subject-view .modal-lg {
        width: 820px;
        max-width: none;
    }
}

@media (max-width: 991px) {
    .breadcrumb {
        padding: 0px 0 15px !important;
    }
}

@media (min-width: 768px) {
    .btn-wider {
        width: 200px;
    }
}
