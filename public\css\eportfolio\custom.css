<style>
.breadcrumb>.breadcrumb-item>a,
.breadcrumb>.breadcrumb-item,
.breadcrumb>.breadcrumb-item.active,
.breadcrumb>.breadcrumb-item+.breadcrumb-item:before {
	color: #fff;
}

.fa-arrows{
	color: #9c9c9d;
}

.custom-black-tabs>li {
	min-width: 50%;
	padding: 15px;
}

.section {
	/*width: 350px;*/
}

.back {
	font-family: FontAwesome;
	content: "\f060";
	right: 13px;
	top: 36%;
	color: #507ef3;
	float: left;
	cursor: pointer;
	line-height: 22px;
}

.content_head {
	cursor: pointer;
}

.card-group .card-header .card-title>a:after {
	display: none;
}

.border-top{
	border-top: 1px solid #ededed;
}

.no-line-height {
	line-height: normal;
}

.bg-grey {
	background-color: #f2f2f4;
}

.br-1 {
	border-right: 1px solid #ededed;
}

.text-logo-box {
	height: 150px;
}

.logo-box {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.logo-in-text {
	border: 4px solid #386af3;
	padding: 0px 15px;
	font-size: 30px;
	color: #386af3;
}

.logo-to-upload {
	border: 4px solid #e4e5e7;
	padding: 5px 10px;
	font-size: 25px;
	color: #e4e5e7;
}

.fileUpload input.upload {
	position: absolute;
	top: 0;
	right: 0;
	margin: 0;
	padding: 0;
	font-size: 20px;
	cursor: pointer;
	opacity: 0;
	filter: alpha(opacity=0);
	width: 55px;
	height: 46px;
	cursor: pointer;
}

.eport-input-text {
	cursor: pointer;
	border: none;
}

.eport-input-text:focus {
	background: white;
}

.w-full {
	width: 100%;
}

.eport-select {
	background: transparent;
	border: none;
}

.eport-select:focus {
	background: transparent;
}

.align-center {
	padding-top: calc(0.5rem - 1px * 2);
	padding-bottom: calc(0.5rem - 1px * 2);
	margin-bottom: 0;
}

.align-center-select {
	padding-top: calc(0.8rem - 1px * 2);
	padding-bottom: calc(0.8rem - 1px * 2);
	margin-bottom: 0;
}

.colour-box {
	height: 50px;
	width: 50px;
	background: white;
}

.colour-box-title {
	padding-top: calc(1.5rem - 1px * 2);
	padding-bottom: calc(1.5rem - 1px * 2);
	margin-bottom: 0;
}

.switchery-small {
	width: 45px;
	float: right;
}

.blue,
.blue:focus {
	color: #507ef3;
}

.d-flex {
	display: inline-flex;
}

.card-actions>.btn {
    background-color: #000;
    width: 18px;
    height: 18px;
    text-align: center;
    border-radius: 50%;
    padding: 0;
}

.card-actions>.btn>i {
    color: #fff;
    line-height: 18px;
    font-size: 10px;
}

.card-actions>.btn+.btn {
    margin-left: 4px;
}

.card-eport * {
    line-height: normal;
}

.new-section-lh{
	line-height: 17px !important;
}

/*Content Section*/


@media (max-width: 991px) {}

@media (max-width: 574px) {

}
</style>