@import url(https://fonts.googleapis.com/css?family=Noto+Sans:400,700);
@import url(https://fonts.googleapis.com/css?family=Sanchez:400italic,400);
@import url(https://fonts.googleapis.com/css?family=Roboto:100);
@font-face {
    font-family: 'Bebas New Regular';
    src: url(/fonts/BebasNeue-Regular.otf?a92d19b5693c162bf887ee4ab461052e);
}

@font-face {
    font-family: '<PERSON>';
    /* src: url(../fonts/Oswald-Regular.ttf); */
    src: url(/fonts/Oswald-Light.ttf?8716983bbdbb3e6f02c1fe2f2cb64730);
}

@font-face {
    font-family: 'PT Sans';
    src: url(/fonts/PT_Sans-Regular.ttf?983a32f60c185f37dab77a40518402c8);
}

@font-face {
    font-family: 'Roboto';
    src: url(/fonts/Roboto-Light.ttf?8be2d1487420548b3b8ddba68270b26b);
    font-weight: 300;
}

@font-face {
    font-family: 'Roboto';
    src: url(/fonts/Roboto-Regular.ttf?a8d6ac03c7b96b7acb6228ff2676139d);
}

@font-face {
    font-family: 'Roboto';
    src: url(/fonts/Roboto-Medium.ttf?7429a63c09f79a1760b0233e3e46f776);
    font-weight: 500;
}

@font-face {
    font-family: 'Roboto';
    src: url(/fonts/Roboto-Bold.ttf?75371f53f06181df75f16f2a140533e5);
    font-weight: bold;
}

@font-face {
    font-family: 'Termina';
    src: url(/fonts/termina-regular.woff?0df04b4407cdb6f3abf63d8c639797b6);
}

.loader-overlay {
    position: absolute;
    height: 100%;
    width: 100%;
    background: rgba(255, 255, 255, 0.5);
}

.loader {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    font-size: 5px;
    margin: 0 auto;
    text-indent: -9999em;
    background-image: url(/images/loader.gif?11eb55706d47c8496552a0fcf940ff21);
}


.loader,
.loader:after {
    width: 180px;
    height: 60px;
    border: none;
}

.loader-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    z-index: 99999;
}

body,
p {
    /*letter-spacing: 0.05em;*/
    letter-spacing: 0.07em;
    line-height: 24px;
    font-size: 16px;
    font-family: 'Roboto', sans-serif !important;
}

p {
    margin-bottom: 0px;
}


.form-control {
    font-family: 'Roboto', sans-serif !important;
    letter-spacing: 0.98px;
}

.l-h-24 {
    line-height: 24px;
}

.l-h-28 {
    line-height: 28px;
}

.timeline-content p,
.timeline-content ul {
    line-height: 24px;
}

.template-body ul li,
.template-body p {
    line-height: 28px;
}

.text-black {
    color: #000 !important;
}

.text-dark {
    color: #2c2c2c !important;
}

a {
    /*color: #0A0AFD;*/
    color: inherit;
    font-weight: bold;
}

a:active,
a:focus,
a:hover {
    /*color: #243dff;*/
    color: inherit;
    text-decoration: underline;
}

h1,
h2,
h3,
h4,
h5,
h6,
.windows h1,
.windows h2,
.windows h3,
.windows h4,
.windows h5 {
    font-weight: 400;
}

.italic {
    font-style: italic !important;
}

.alert-success {
    background-color: #0A0AFD;
    color: #fff;
    font-size: 14px;
    letter-spacing: 1px;
    border-color: #0A0AFD;
}

.alert-success .close {
    background: none;
    color: #fff;
    font-size: 20px;
    top: 0;
    opacity: 0.9;
    font-weight: normal;
}

.alert-success .close:before {
    content: "\00D7";
}

.line-through {
    text-decoration: line-through;
}

.text-blue {
    color: #0A0AFD !important;
}

.text-orange {
    color: #fd3806 !important;
}

.bg-black {
    background-color: #000 !important;
    color: #fff;
}

.bg-blue {
    background-color: #0A0AFD !important;
}

.bg-orange {
    background-color: #fd3806 !important;
}


.no-hover-color:hover {
    color: unset;
}

.bebas-new {
    font-family: 'Bebas New Regular' !important;
}

.oswald {
    font-family: 'Oswald', sans-serif !important;
}

.pt-sans {
    font-family: 'PT Sans', sans-serif !important;
}

.roboto {
    font-family: 'Roboto', sans-serif !important;
}

.fs-18 {
    font-size: 18px !important;
}

.fs-20 {
    font-size: 20px !important;
}

.fs-22 {
    font-size: 22px !important;
}

.fs-24 {
    font-size: 24px !important;
}

.fs-26 {
    font-size: 26px !important;
}

.fs-28 {
    font-size: 28px !important;
}

.fw-500 {
    font-weight: 500 !important;
}

.fw-600 {
    font-weight: 600 !important;
}

.ls-0 {
    letter-spacing: 0 !important;
}

.ls-1 {
    letter-spacing: 1px !important;
}

.ls-2 {
    letter-spacing: 2px !important;
}

.ls-3 {
    letter-spacing: 3px !important;
}

.ls-4 {
    letter-spacing: 4px !important;
}

.ls-5 {
    letter-spacing: 5px !important;
}

.ls-10 {
    letter-spacing: 10px !important;
}

.pre-wrap {
    white-space: pre-wrap !important;
}

.pre-line {
    white-space: pre-line !important;
}

.overflow-visible {
    overflow: visible !important;
}

.full-width {
    width: 100% !important;
}

.tooltip-inner {
    text-align: left;
}

.logo-v-middle {
    position: absolute;
    top: 50%;
    padding: 0 15px;
    left: 0;
    transform: translateY(-50%);
}

.thumbnail-wrapper.user-initials {
    background-color: #333;
    color: #ccc;
    font-weight: bold;
    letter-spacing: 1px;
}

.thumbnail-wrapper.user-initials:hover {
    background-color: #222;
}

.thumbnail-wrapper.d32 {
    line-height: 32px;
}

/* [custom toggle switch css] */

.toggleswitch {
    width: 75px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.toggleswitch-checkbox {
    display: none;
}

.toggleswitch-label {
    display: block;
    overflow: hidden;
    cursor: pointer;
    border-radius: 50px;
    position: relative;
    margin-bottom: 0;
}

.toggleswitch-inner {
    display: block;
    width: 200%;
    margin-left: -100%;
    transition: margin 0.3s ease-in 0s;
}

.toggleswitch-inner:before,
.toggleswitch-inner:after {
    display: block;
    float: left;
    width: 50%;
    height: 30px;
    padding: 0;
    line-height: 30px;
    font-size: 16px;
    font-family: "FontAwesome";
    box-sizing: border-box;
}

.toggleswitch-inner:before {
    content: "\f00c";
    padding-left: 5px;
    background-color: #0A0AFD;
    color: #fff;
    text-align: left;
}

.toggleswitch-inner:after {
    content: "\f00d";
    padding-right: 5px;
    background-color: #000;
    color: #555;
    text-align: right;
}

.toggleswitch-switch {
    display: block;
    width: 20px;
    margin: 5px;
    background: #fff;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 60%;
    border-radius: 50%;
    transition: all 0.3s ease-in 0s;
}

.toggleswitch-checkbox:checked+.toggleswitch-label .toggleswitch-inner {
    margin-left: 0;
}

.toggleswitch-checkbox:checked+.toggleswitch-label .toggleswitch-switch {
    right: 0px;
}

/* [/custom toggle switch css] */


.toggleswitch.tile-switch {
    position: absolute;
    z-index: 1;
    right: 20px;
    top: 5px;
    width: 60px;
}

.toggleswitch.tile-switch .toggleswitch-inner:before,
.toggleswitch.tile-switch .toggleswitch-inner:after {
    height: 25px;
    line-height: 25px;
    font-size: 12px;
}

.toggleswitch.tile-switch .toggleswitch-switch {
    width: 15px;
}

/* [custom scrollbar css] */

.custom-scrollbar,
.fancybox-inner {
    scrollbar-width: thin;
    scrollbar-color: #999 #595959;
}

/* width */
.custom-scrollbar::-webkit-scrollbar,
.fancybox-inner::-webkit-scrollbar {
    width: 10px;
}

/* Track */
.custom-scrollbar::-webkit-scrollbar-track,
.fancybox-inner::-webkit-scrollbar {
    background: #595959;
}

/* Handle */
.custom-scrollbar::-webkit-scrollbar-thumb,
.fancybox-inner::-webkit-scrollbar-thumb {
    background: #999;
    border-radius: 100px;
    border: none;
}

/* Handle on hover */
.custom-scrollbar::-webkit-scrollbar-thumb:hover,
.fancybox-inner::-webkit-scrollbar-thumb:hover {
    background: #bbb;
}

.fr-popup.fr-active {
    z-index: 9999 !important;
}

/* [/custom scrollbar css] */

body.menu-behind .header .brand>img {
    width: 230px;
}

.uppercase {
    text-transform: uppercase !important;
}

.font-medium {
    font-weight: 500;
}

.screen-height {
    height: 100vh !important;
}

.row-negative-margin {
    margin-left: -15px !important;
    margin-right: -15px !important;
}

.multiselect-container>li>a>label {
    color: inherit !important;
    cursor: default !important;
}

.multiselect-container>li:not(.multiselect-all):not(.multiselect-group)>a>label {
    font-weight: normal !important;
}

.form-group-default-multiselect .multiselect-native-select ul {
    z-index: 1052 !important;
}

.label {
    display: inline-block;
}

.label-warning {
    color: #555;
}

.label-dark {
    color: #fff;
    background-color: #000;
}

.label.custom-label {
    padding: 9px 13px;
    border-radius: 1px;
    position: relative;
    margin-bottom: 4px;
}

.label.custom-label>.remove {
    position: absolute;
    top: 0px;
    right: 2px;
    font-weight: normal;
}

.radio input[type=radio],
.checkbox input[type=checkbox] {
    position: absolute;
}

.radio label,
.checkbox label {
    text-align: left;
}

.custom-black-radio {
    margin: .7rem;
}

.custom-black-radio>label {
    margin-bottom: 0;
}

.custom-black-radio input[type=checkbox],
.custom-black-radio input[type=radio] {
    position: absolute;
    opacity: 0;
}

.custom-black-radio input[type=checkbox]+label:before,
.custom-black-radio input[type=radio]+label:before {
    content: '';
    background: #f4f4f4;
    border-radius: 100%;
    border: 2px solid #000;
    display: inline-block;
    width: 18px;
    height: 18px;
    position: relative;
    top: -.2em;
    margin-right: 1em;
    vertical-align: top;
    cursor: pointer;
    text-align: center;
    transition: all 250ms ease;
    font-size: 0;
}

.custom-black-radio input[type=checkbox]:checked+label:before,
.custom-black-radio input[type=radio]:checked+label:before {
    background-color: #000;
    box-shadow: inset 0 0 0 2px #f4f4f4;
}

.custom-black-radio input[type=checkbox]:focus+label:before,
.custom-black-radio input[type=radio]:focus+label:before {
    outline: 0;
    border-color: #000;
}

.custom-black-radio input[type=checkbox]:disabled+label:before,
.custom-black-radio input[type=radio]:disabled+label:before {
    box-shadow: inset 0 0 0 2px #f4f4f4;
    border-color: #b4b4b4;
    background: #b4b4b4;
}

.custom-black-radio input[type=checkbox]+label:empty:before,
.custom-black-radio input[type=radio]+label:empty:before {
    margin-right: 0;
}



.custom-sharp-checkbox label {
    min-width: 15px;
    min-height: 15px;
    margin-right: 0;
    padding-left: 25px;
    line-height: normal;
    vertical-align: middle;
}

.custom-sharp-checkbox.checkbox-md label {
    min-width: 20px;
    min-height: 20px;
    padding-left: 30px;
}


.custom-sharp-checkbox input[type=checkbox]:checked+label:before {
    border-width: 7.5px;
}

.custom-sharp-checkbox.checkbox-md input[type=checkbox]:checked+label:before {
    border-width: 9.5px;
}

.custom-sharp-checkbox label::before {
    top: 50% !important;
    transform: translateY(-50%);
    border-color: #000;
}

.custom-sharp-checkbox label::after {
    left: 3px;
    top: calc(50% + 1px);
    transform: translateY(-50%);
    font-size: 10px;
}

.custom-sharp-checkbox.checkbox-md label::after {
    left: 4px;
    top: calc(50% + 4px);
    transform: translateY(-50%);
    font-size: 13px;
}


.custom-sharp-checkbox label:before,
.custom-sharp-checkbox label::after {
    width: 15px;
    height: 15px;
    border-radius: 0;
    margin-right: 0;
}

.custom-sharp-checkbox.checkbox-md label:before,
.custom-sharp-checkbox.checkbox-md label::after {
    width: 20px;
    height: 20px;
}

.custom-sharp-checkbox input[type=checkbox]:focus+label:before {
    background-color: #fff;
    border-color: #000;
}


td .radio label,
td .checkbox label {
    text-align: center;
}

.border-grey {
    border-color: #bbb !important;
}

.select-view-tab>a {
    text-transform: uppercase;
    color: #888;
    font-weight: bold;
    font-size: 12px;
}

.select-view-tab>a+a {
    margin-left: 15px;
}

.select-view-tab>a.active,
.select-view-tab>a:active,
.select-view-tab>a:hover,
.select-view-tab>a:focus {
    color: #000;
}


input.hide-spinner[type=number]::-webkit-inner-spin-button,
input.hide-spinner[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.toggle-breadcrumb {
    font-size: 22px;
    font-weight: bold;
    position: absolute;
    display: inline;
    transition: transform 0.3s ease 0s;
}

.breadcrumb {
    font-family: 'Roboto', sans-serif;
    padding: 5px 15px;
}

.toggle-breadcrumb+div>.breadcrumb {
    padding: 0px;
}

.toggle-breadcrumb+.breadcrumb .breadcrumb .breadcrumb-item {
    margin-left: 5px;
    margin-right: 5px;
    font-size: 10.5px !important;
    letter-spacing: .06em;
    font-weight: 500;
    color: #7b7d82;
}

.breadcrumb>.breadcrumb-item>a,
.breadcrumb>.breadcrumb-item,
.breadcrumb>.breadcrumb-item.active,
.breadcrumb>.breadcrumb-item+.breadcrumb-item:before {
    letter-spacing: 1.5px;
    font-weight: bold;
    color: #000;
    line-height: normal;
    vertical-align: text-top;
    display: inline-block;
}

.content>.jumbotron {
    margin-bottom: 0;
    background-color: transparent;
}

.page-container .page-content-wrapper {
    min-height: 100vh;
}

.content>.container-fluid,
.content>.container {
    padding-top: 2rem;
    /* min-height: calc(100vh - 130px); */
}

.close-btn {
    letter-spacing: 2px;
    font-size: 14px;
}

.page-container .page-content-wrapper .footer {
    border-top: 1px solid rgba(87, 87, 87, .07);
}

.copyright {
    border-top: none;
}

.copyright>p {
    font-size: 12px !important;
}

.blue-bg-heading {
    font-family: 'Oswald', sans-serif;
    font-weight: bold;
    padding: 2px 15px;
    display: inline-block;
    letter-spacing: 2.5px;
    color: #fff;
    margin: 0 0 0 12px;
    background-color: #0A0AFD;
    text-transform: uppercase;
}


.custom-btn,
.btn.btn-primary,
.btn.btn-orange,
.btn.btn-black,
.custom-btn:focus,
.btn.btn-orange:focus,
.btn.btn-primary:focus,
.btn.btn-black:focus {
    padding-bottom: 8px;
    padding-top: 8px;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 3px;
    font-family: 'Oswald', sans-serif;
    border-radius: 1px;
    font-weight: 700;
    font-size: 12px;
    border: 2px solid !important;
    text-align: center;
    cursor: pointer;
    background-color: transparent !important;
    transition: all 300ms linear;
    width: auto;
    min-width: 120px;
    text-shadow: none;
    box-shadow: none;
}

.custom-btn:hover,
.btn.btn-orange:hover,
.btn.btn-primary:hover,
.custom-btn:active,
.btn.btn-orange:active,
.btn.btn-primary:active {
    text-shadow: none;
    box-shadow: none;
}

.btn.btn-primary,
.btn.btn-black,
.btn.btn-black:focus,
.btn.btn-primary:focus {
    border-color: #000 !important;
    color: #000 !important;
}

.btn.btn-primary:hover,
.btn.btn-primary:active,
.btn.btn-black:hover,
.btn.btn-black:active,
.btn.btn-primary.btn-filled,
.btn.btn-black.btn-filled,
.btn.btn-primary.btn-filled:focus,
.btn.btn-black.btn-filled:focus {
    border-color: #000 !important;
    background: #000 !important;
    color: #fff !important;
}


.custom-btn.btn-black-filled,
.custom-btn.btn-black-filled:focus {
    background-color: #000 !important;
    border-color: #000 !important;
    color: #fff !important;
}

/* .custom-btn.btn-black-filled:hover,
    .custom-btn.btn-black-filled:active {
        background-color: transparent !important;
        color: #000 !important;
    } */

.custom-btn.btn-black-filled:hover,
.custom-btn.btn-black-filled:active,
.btn.btn-primary.btn-filled:hover,
.btn.btn-primary.btn-filled:active {
    background: #fff !important;
    border-color: #fff !important;
    color: #000 !important;
}


.btn.btn-orange,
.btn.btn-orange:focus {
    border-color: #fd3806 !important;
    color: #fd3806 !important;
}

.btn.btn-yellow-filled,
.btn.btn-yellow-filled:focus {
    border-color: #e7ff00 !important;
    background: #e7ff00 !important;
    color: #000 !important;
}

.btn.btn-orange:hover,
.btn.btn-orange:active,
.btn.btn-orange.btn-filled,
.btn.btn-orange.btn-filled:focus {
    border-color: #fd3806 !important;
    background: #fd3806 !important;
    color: #fff !important;
    text-shadow: 0px 0px 0px #fff;
    box-shadow: none;
}

.btn.btn-orange.btn-filled:hover,
.btn.btn-orange.btn-filled:active {
    background: #e02e01 !important;
    border-color: #e02e01 !important;
}

.btn.btn-primary:disabled,
.btn.btn-orange:disabled,
.custom-btn:disabled {
    cursor: not-allowed;
    pointer-events: none;
}

.custom-btn.btn-white,
.custom-btn.btn-white:focus {
    border-color: #fff !important;
    color: #fff !important;
}


.custom-btn.btn-white.checked,
.custom-btn.btn-white:hover,
.custom-btn.btn-white:active,
.custom-btn.btn-white-filled,
.custom-btn.btn-white-filled:focus {
    background-color: #fff !important;
    border-color: #fff !important;
    color: #000 !important;
}

.bg-blue .custom-btn.btn-white.checked,
.bg-blue .custom-btn.btn-white:hover,
.bg-blue .custom-btn.btn-white:active,
.bg-blue .custom-btn.btn-white-filled,
.bg-blue .custom-btn.btn-white-filled:focus,
.bg-blue-lite .custom-btn.btn-white.checked,
.bg-blue-lite .custom-btn.btn-white:hover,
.bg-blue-lite .custom-btn.btn-white:active,
.bg-blue-lite .custom-btn.btn-white-filled,
.bg-blue-lite .custom-btn.btn-white-filled:focus {
    color: #0A0AFD !important;
}

.bg-orange .custom-btn.btn-white:hover,
.bg-orange .custom-btn.btn-white:focus {
    color: #fd3806 !important;
}

.custom-btn.btn-white-filled:hover,
.custom-btn.btn-white-filled:active {
    background-color: #f3f3f3 !important;
    border-color: #f3f3f3 !important;
}

.custom-btn.btn-blue-filled,
.custom-btn.btn-blue-filled:focus {
    background-color: #0A0AFD !important;
    border: none !important;
    color: #fff !important;
    letter-spacing: 1.5px;
    padding: 5px 10px;
    font-size: 10px;
    min-width: unset;
    box-shadow: none;
}

.custom-btn.btn-blue-filled:hover,
.custom-btn.btn-blue-filled:active {
    background-color: #fff !important;
    color: #0A0AFD !important;
}

.custom-btn.btn-blue:hover,
.custom-btn.btn-blue:active {
    background-color: #0A0AFD !important;
    color: #fff !important;
}

.custom-btn.btn-rust-filled,
.custom-btn.btn-rust-filled:focus {
    background-color: #ff1e04 !important;
    border-color: #ff1e04 !important;
    color: #84143a !important;
}

.custom-btn.btn-rust-filled:hover,
.custom-btn.btn-rust-filled:active {
    /* background-color: transparent !important;
        color: #ff1e04 !important; */
    background-color: #e71700 !important;
    border-color: #e71700 !important;
}


.btn-custom-sm {
    padding: 3px 10px !important;
    font-size: 10px !important;
    letter-spacing: 2px !important;
    min-width: 80px !important;
}


.custom-orange-tabs,
.custom-black-tabs {
    list-style: none;
    display: flex;
    text-align: center;
    padding: 0;
}

.custom-orange-tabs>li,
.custom-black-tabs>li {
    padding: 1px 5px;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-family: 'Oswald', sans-serif;
    border-radius: 1px;
    font-weight: 700;
    font-size: 12px;
    border: 2px solid;
    text-align: center;
    cursor: pointer;
    background-color: transparent;
    transition: all 300ms linear;
    min-width: 270px;
}

.custom-orange-tabs>li+li,
.custom-black-tabs>li+li {
    margin-left: -2px;
}

.custom-black-tabs>li {
    border-color: #000;
    color: #000;
}

.custom-black-tabs>li.active,
.custom-black-tabs>li:hover,
.custom-black-tabs>li:focus {
    border: 2px solid #000;
    background: #000;
    color: #fff;
}

.custom-orange-tabs>li {
    border-color: #fd3806;
    color: #fd3806;
}

.custom-orange-tabs>li.active,
.custom-orange-tabs>li:hover,
.custom-orange-tabs>li:focus {
    border: 2px solid #fd3806;
    background: #fd3806;
    color: #fff;
}

.radio.radio-primary input[type=radio]:checked+label:before,
.checkbox.check-primary input[type=checkbox]:checked+label:before {
    border-color: #1a1a1a;
}


/***** Heart checkbox css starts ******/


.like-heart>input,
.bookmark {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}

.like-heart>label {
    font-size: 16px;
    position: relative;
    margin: 0;
    cursor: pointer;
    background-color: #eee;
    color: #9d9d9d;
    display: block;
    height: 36px;
    width: 36px;
    border-radius: 50%;
    transition: all 0.4s;
    text-align: center;
}

.like-heart.like-heart-small>label {
    font-size: 12px;
    height: 25px;
    width: 25px;
}

.like-heart>label:hover,
.like-heart>label:focus,
.like-heart>input:checked+label {
    background-color: #000;
    color: #fff;
}

.like-heart>label:before,
.like-heart>label:after {
    transition: opacity 0.3s, transform 0.4s cubic-bezier(.0, -.41, .19, 2.5);
    font-family: "FontAwesome";
    position: absolute;
    right: 0;
    left: 0;
    line-height: 37px;
}

.like-heart.like-heart-small>label:before,
.like-heart.like-heart-small>label:after {
    line-height: 27px;
}

.like-heart>label:before {
    content: "\f08a";
    transform: scale(0.95);
}

.like-heart>input:checked+label:before {
    opacity: 0;
}

.like-heart>label:after {
    content: "\f004";
    opacity: 0;
    transform: scale(0.5);
}

.like-heart>input:checked+label:after {
    opacity: 1;
    transform: scale(0.95);
}

.float-right {
    float: right;
}

/***** Heart checkbox css starts ******/


iframe:not(.no-full-width) {
    width: 100% !important;
}

.superscript-cross>img {
    width: 20px;
    margin-left: -10px;
    vertical-align: super;
}

.dark-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
}

.nav-tabs-simple>li>a:after {
    background-color: #000;
}

ul.menu-items li:first-child {
    margin-top: 30px;
    /* margin-top: 60px; */
}

body.menu-behind .header .brand {
    width: auto;
    text-align: center;
}

.profile-dropdown {
    right: auto !important;
    left: auto !important;
}

.page-sidebar .sidebar-menu .menu-items>li.active>a>.arrow::before {
    transform: rotate(-90deg);
}

.form-group+.error {
    margin-top: -10px;
    margin-bottom: 10px;
}

.form-group-default.has-error {
    background-color: #F7E3E3;
}

.form-horizontal .form-group:last-child {
    border-bottom: 1px solid #e1e1e1;
}

select.form-control {
    -webkit-appearance: menulist;
    -moz-appearance: menulist;
    appearance: menulist;
}

/* .form-group-default.focused {
        border: 1px solid rgba(0,0,0,.07);
        background-color: #fff;
        box-shadow: 0px 0px 15px 0px #E2DEEF;
        }  */

/* .form-group-default.focused label {
        opacity: 1;
        } */


/* Select 2 customization Starts from here*/

.form-group-default.form-group-default-select2 .select2-container .select2-selection--multiple {
    padding-top: 20px;
    height: auto;
    border: 0 !important;
}

.select2-container .select2-selection {
    background-color: transparent;
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #ddd !important;
}

select.full-width+.error-help-block+.select2-container,
select.full-width+.error-help-block+.select2-container .select2-selection {
    width: 100% !important;
    background-color: transparent;
}

textarea {

    min-height: 60px !important;
}

textarea.form-group,
textarea:not(.cke_source) {
    height: auto !important;
}

.form-group-default.form-group-default-select2>label.error {
    position: relative;
    padding-top: 0;
    top: 25px;
}

.form-group-default-select2 .error-help-block {
    z-index: 1;
    left: 12px;
    top: 24px;
    position: relative;
}


/* Select 2 customization Ends Here*/

.error-help-block {
    color: #f35958;
    font-size: 14px;
}

.modal-open .select2-container {
    z-index: 1050;
}

.modal-body .form-group-default.form-group-default-select2>label {
    z-index: 1051;
}

ul.pagination li.active {
    color: #0090d9;
}


.no-list {
    list-style: none;
}

.list-blue-X {
    width: 13px;
    margin-right: 5px;
}

.custom-table-heading th {
    font-family: 'Oswald', sans-serif !important;
    letter-spacing: 2px !important;
    font-size: 16px !important;
    color: #000 !important;
}

table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:before {
    content: none;
}

table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_desc_disabled:before {
    transform: rotate(180deg);
}

.dataTables_wrapper .dataTables_paginate ul>li>a {
    opacity: 1;
}

.page-item.active .page-link {
    background-color: #000;
    border-color: #000;
}

.note-editor .btn-toolbar {
    display: block;
}

.table.custom-datatable tbody tr td {
    padding: 15px 10px;
}

.table.custom-datatable td,
.table.custom-datatable th {
    vertical-align: middle !important;
}


/* calendar styling */

.fc .fc-center>* {
    font-family: 'Oswald', sans-serif;
    text-transform: uppercase;
    line-height: 1;
    letter-spacing: 2.5px !important;
    color: #fff;
    background-color: #0A0AFD;
    font-size: 18px;
    padding: 5px 7px 5px 8px;
}

.fc-button {
    padding: 0 15px !important;
    min-height: 30px;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 2px;
    line-height: normal;
    font-family: 'Oswald', sans-serif;
    border-radius: 1px !important;
    font-weight: 700;
    font-size: 11px !important;
    border: 2px solid #fd3806 !important;
    color: #fd3806 !important;
    text-align: center;
    cursor: pointer;
    background: transparent;
    transition: all 300ms linear;
    text-shadow: none;
    box-shadow: none;
    z-index: 799 !important;
}

.fc-button:hover,
.fc-button:focus,
.fc-button.fc-state-active {
    border: 2px solid #fd3806 !important;
    background: #fd3806 !important;
    color: #fff !important;
    box-shadow: none;
}

.fc-button.fc-state-disabled {
    pointer-events: none;
}


/* calendar stylibg ends */

/****** Style Star Rating Widget Starts Here*****/

fieldset.rating,
.rating>label {
    margin: 0;
    padding: 0;
}

.rating {
    border: none;
    float: left;
}

.rating>input {
    display: none;
}

.rating>label:before {
    margin: 5px;
    font-size: 1.25em;
    font-family: FontAwesome;
    display: inline-block;
    content: "\f00c";
}

.rating>label {
    color: #ddd;
    float: right;
}

.rating>label:hover+.rating>input:checked~label {
    color: #353535;
}

.rating>input:checked~label {
    color: #000;
}

.rating>label:hover,
.rating>label:hover~label {
    color: #000;
    transform: scale(1.15);
    transition: transform .2s ease;
}

/****** Style Star Rating Widget Ends Here*****/

/***** Custom Accordion Css Start's Here*****/

.custom-accordion .card-body {
    padding: 15px;
    border: 2px solid #000;
    border-top: none;
}

.custom-accordion .card>.card-header {
    color: #333;
    background-color: #fff;
    border-color: #e4e5e7;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    min-height: auto;
}

.custom-accordion .card>.card-header>.mb-0 {
    margin: 0 !important;
}

.custom-accordion .card>.card-header a {
    display: block;
    padding: 10px 15px 10px 35px;
    opacity: 1;
    white-space: normal;
}

.custom-accordion .card>.card-header a:after {
    content: "";
    position: absolute;
    right: 10px;
    font-family: "Glyphicons Halflings";
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: transform 0.25s linear;
    -webkit-transition: -webkit-transform 0.25s linear;
}

.custom-accordion .card>.card-header a[aria-expanded="true"],
.custom-accordion .card>.card-header a:hover {
    background-color: #000 !important;
    color: #fff;
}

.custom-accordion .card>.card-header a[aria-expanded="true"]:after {
    content: "\2212";
    transform: rotate(180deg);
    font-weight: bolder;
    font-size: 29px;
}

.custom-accordion .card>.card-header a[aria-expanded="false"]:after {
    content: "\002b";
    transform: rotate(90deg);
    font-weight: bolder;
    font-size: 29px;
}

.custom-accordion.small-accordion .card>.card-header a {
    height: 80px;
    font-size: 16px;
    letter-spacing: 1.5px;
    border: none !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
    white-space: normal;
    padding: 10px 50px;
    background-color: #F4F4F4 !important;
    font-weight: normal;
}

.custom-accordion.small-accordion .card>.card-header a:after {
    color: #0A0AFD;
}

/* .custom-accordion.small-accordion .card>.card-header a:after {
        position: static;
        display: contents;
        font-size: 15px;
        } */

.custom-accordion.small-accordion .card-block {
    border: none;
    background-color: #F4F4F4;
}

.custom-accordion.small-accordion .card>.card-header a[aria-expanded="true"],
.custom-accordion.small-accordion .card>.card-header a:hover,
.custom-accordion.small-accordion .card>.card-header a:focus {
    color: #000 !important;
    box-shadow: none;
}

.custom-accordion.small-accordion ul {
    list-style: none;
    padding-left: 0px;
}

.custom-accordion.small-accordion ul>li {
    padding-left: 0;
}

.custom-accordion.small-accordion .card-block a {
    color: #000;
}

.custom-accordion.small-accordion .card-block a:hover {
    /* color:#000; */
    font-weight: 800;
}

.custom-accordion.small-accordion ul>li:before {
    content: "+";
}

.btn-collapse {
    outline: none;
    padding-bottom: 8px;
    padding-top: 9px;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 3px;
    font-family: 'Oswald', sans-serif;
    font-weight: 700;
    font-size: 12px;
    border: 2px solid #000;
    border-bottom: 2px solid #000 !important;
    color: #000;
    cursor: pointer;
    text-align: left;
    /* background: rgba(0,0,0,0); */
    background-color: #fff !important;
    transition: all 300ms linear;
    border-radius: 0px;
}

.btn-collapse:hover {
    border: 2px solid #000;
    background-color: #000 !important;
    color: #fff !important;
}

/***** Custom Accordion Css End's Here*****/

/***** Tiles Css Start's Here*****/

.profile-cards {
    margin: 0 -5px;
}

.profile-cards li {
    width: 200px;
    display: inline-table;
    margin-top: 10px;
}

.profile-cards .percentage {
    background-position: center;
    height: 190px;
    line-height: 190px;
    background-size: 100%;
    background-repeat: no-repeat;
    /* box-shadow: 0 8px 6px -6px rgba(0, 0, 0, 0.6); */
    color: #fff;
    transition: all .2s ease;
}

.profile-cards li>a:hover .percentage,
.profile-cards li>a:focus .percentage {
    background-size: 120%;
}

/* .profile-cards>li>a:hover .percentage .box-overlay,
    .profile-cards>li>a:focus .percentage .box-overlay {
        background-color: transparent;
        } */

.profile-cards .percentage>.box-overlay {
    /* background-color: rgba(0, 0, 0, 0.3); */
    font-weight: bold;
    font-size: 14px;
}

.profile-cards .percentage>.box-industry {
    background-color: rgba(0, 0, 0, 0.3);
    font-weight: bold;
    font-size: 14px;
}

.profile-cards .topic {
    font-weight: bold;
    margin: 10px 0;
    line-height: 15px;
}

.new-cards .topic {
    color: #000;
    font-weight: normal;
    letter-spacing: .3px;
}


.template-tiles {
    display: flex;
    display: -webkit-flex;
    /* Safari */
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    /* Safari 6.1+ */
    -ms-flex-wrap: wrap;
    /* IE 10 */
}

.template-tiles>li {
    width: 230px;
    padding: 0 20px;
    margin-top: 10px;
    position: relative;
}

.profile-cards li,
.new-cards li,
.template-tiles li {
    vertical-align: top;
}

.template-tiles .tile-content {
    background-position: center;
    height: 190px;
    line-height: 190px;
    background-size: 100%;
    background-repeat: no-repeat;
    color: #fff;
    transition: all .2s ease;
}

.template-tiles>li>a:hover .tile-content,
.template-tiles>li>a:focus .tile-content {
    background-size: 120%;
}

.new-cards li {
    padding: 0 15px;
    width: 220px;
}

.new-cards .tile-title {
    color: #000;
    margin-top: 10px;
    margin-bottom: 10px;
    line-height: normal;
}

.hover-colored .percentage {
    overflow: hidden;
    position: relative;
}

.hover-colored .percentage>*:not(.tile-label) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
}

.hover-colored .percentage>img {
    transition: all .2s ease-in-out;
    filter: grayscale(100%);
}

.hover-colored:hover img {
    filter: grayscale(0%);
    transform: translate(-50%, -50%) scale(1.15) !important;
    transform-origin: center;
    -webkit-transform-origin: center;
}

.blue-check {
    line-height: 26px;
    width: 25px;
    height: 25px;
}

.template-tiles .tile-title {
    margin: 10px 0;
    line-height: 15px;
    color: #000;
    font-weight: normal;
    letter-spacing: .3px;
}

/***** Tiles Css End's Here*****/

/***** Populate Dashboard Css Start's Here*****/

div#sourceFields,
.hideme {
    display: none;
}

.fc-field {
    margin: 0 0 0 0;
    padding: 5px;
    font-size: 1.2em;
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 2px;
}

.fc-selected,
.fc-field:hover,
.fc-field:focus {
    background-color: #08c;
    color: #fff;
}

.fc-field-list {
    margin: 0;
    padding: 3px 3px 3px 3px;
    margin-right: 10px;
    border: 1px solid #ddd;
    height: 230px;
    width: 48%;
    overflow: auto;
    border-radius: 3px;
}

.fc-field.ui-sortable-placeholder {
    visibility: hidden !important;
}

.fc-field+.fc-field {
    margin-top: 3px;
}

.fc-source-fields {
    float: left;
}

.fc-destination-fields {
    float: right;
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
    background: #f3f3f3;
    color: #737373;
}

/***** Populate Dashboard Css End's Here*****/

.selected-plan-list>li {
    margin-bottom: 15px;
    line-height: 18px;
    font-size: 13px;
    font-weight: bold;
}

.plan-answer {
    font-weight: normal;
    margin-top: 3px;
}

/* .modal-backdrop.show {
    opacity: .8;
    }

    .modal-backdrop {
    background-color: #fff;
    } */

#modelStudentNotes .modal-header {
    text-align: left;
}

.card-refresh {
    cursor: pointer;
}

/*Timeline CSS*/

.custom-timeline .social-card {
    background: #e7e7e7;
    border-color: #fff;
}

.custom-timeline .social-card .card-header {
    border-bottom: 1px solid #fff;
    background-color: #e7e7e7;
}

.custom-timeline .social-card .card-footer {
    border-top: 1px solid #fff;
    background-color: #e7e7e7;
}

.custom-timeline .social-card.share .card-footer:hover {
    background: inherit;
    cursor: default;
}

.custom-timeline .timeline::before {
    background: #e7e7e7;
}

.custom-timeline .timeline-point.small {
    background: #fff;
    border-color: #e7e7e7;
}

.custom-timeline .timeline-point.bg-master-darker {
    background-color: #1a1a1a;
}

/*Timeline CSS End*/

.page-sidebar .sidebar-menu .menu-items li>a {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 75%;
}

/* Thumbs up/down radio button css start's here */

#thumbsUp+label:before {
    content: "\f087";
    font-family: FontAwesome;
    border: none;
    font-size: 28px;
}

#thumbsUp:checked+label:before {
    content: "\f164";
    background: none;
}

#thumbsDown+label:before {
    content: "\f088";
    font-family: FontAwesome;
    border: none;
    font-size: 28px;
}

#thumbsDown:checked+label:before {
    content: "\f165";
    background: none;
}

/* Thumbs up/down radio button css end's here */

/* Footer custom button CSS  starts*/

.fixed-footer {
    /* background-color: #8F8D8D; */
    background-color: #e7e7e7;
    padding: 10px;
}

.btn.btn-footer {
    /* background-color: #000 !important; */
    outline: none;
    padding-bottom: 8px;
    padding-top: 9px;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 3px;
    font-family: 'sans-serif', Montserrat;
    font-weight: 700;
    font-size: 12px;
    border: 2px solid #000;
    color: #000;
    text-align: center;
    cursor: pointer;
    background-color: transparent;
    transition: all 300ms linear;
}

.btn.btn-footer:hover {
    background-color: #000 !important;
    color: #fff;
}

/* Footer custom button CSS  ends here*/

/* sidebar customization */

.page-sidebar {
    background-color: #000;
}

.page-sidebar .sidebar-header {
    background-color: #000;
    border-bottom: 1px solid #000;
}

.page-sidebar .sidebar-menu .menu-items>li ul.sub-menu {
    background-color: #0c0c0c;
}


.inner-template-banner {
    background-size: 100%;
    background-repeat: no-repeat;
    height: 20vw;
    transition: opacity .3s ease;
    background-position: center
}

.custom-container {
    max-width: 1020px;
    margin-right: auto;
    margin-left: auto;
}


.template-body iframe {
    max-width: 100% !important;
}

.template-body img {
    max-width: 100% !important;
    /* height: auto !important; */
}

.card-logo {
    width: 50px;
    margin-left: -5px;
}

.card-title-X {
    width: 35px;
    /* vertical-align: -5px;
        vertical-align: -webkit-baseline-middle; */
    margin-left: -5px;
}

.blue-cross {
    color: #0A0AFD !important;
    vertical-align: super;
    font-size: 22px;
}

.card-title.custom-card-title {
    font-family: 'Oswald', sans-serif !important;
    font-size: 16px !important;
    padding: 2px 7.5px 2px 10px !important;
    letter-spacing: 2.5px !important;
    color: #fff;
    line-height: 25px !important;
    vertical-align: middle;
    background-color: #0A0AFD;
}

.card-title.custom-card-title+.card-controls {
    margin-top: 3px;
}

.plan-report .card-title:not(.custom-card-title) {
    font: 15px 'Oswald';
    color: #000;
    letter-spacing: 3px;
}

.plan-report .card-title:not(.custom-card-title):before {
    font: 16px 'FontAwesome';
    content: '\f068';
    color: #0A0AFD;
    letter-spacing: 3px;
    margin-right: 5px;
}


.card-courses .heading {
    font-size: 18px;
    letter-spacing: 3px;
}

.card-courses .btn-group .btn {
    border-radius: 0;
}

.row-levels .btn-group .btn {
    margin: 0 5px 5px 0 !important;
    min-width: 95px;
    padding: 5px;
}

.row-levels .btn-group .btn {
    background: #fff;
    color: #010afe;
    border: 1px solid #010afe;
}

.row-levels .btn-group .btn.active,
.row-levels .btn-group .btn:hover,
.row-levels .btn-group .btn:focus,
.row-levels .btn-group .btn:active {
    color: #fff;
    background-color: #010afe;
}


.courses-list .courses-list-item {
    margin-bottom: 30px;
}

.courses-list-item .course-name {
    color: #2d2d2d;
    font-size: 16px;
}

.courses-list-item span {
    display: inline-block;
}

.courses-list .courses-list-item .like-icon {
    font-size: 19px;
    position: absolute;
    transform: translateY(50%);
    bottom: 50%;
    right: 0;
    margin: 0;
    cursor: normal;
    background-color: #eee;
    color: #9d9d9d;
    display: block;
    height: 44px;
    width: 44px;
    border-radius: 50%;
    transition: all 0.4s;
    text-align: center;
}

.courses-list .courses-list-item .like-icon:hover,
.courses-list .courses-list-item .like-icon:focus,
.courses-list .courses-list-item .like-icon.liked {
    background-color: #000;
    color: #fff;
}

.courses-list .courses-list-item .like-icon:before,
.courses-list .courses-list-item .like-icon:after {
    transition: opacity 0.3s, transform 0.4s cubic-bezier(.0, -.41, .19, 2.5);
    font-family: "FontAwesome";
    position: absolute;
    right: 0;
    left: 0;
    line-height: 45px;
}

.courses-list .courses-list-item .like-icon:before {
    content: "\f08a";
    transform: scale(0.95);
}

.courses-list .courses-list-item .like-icon.liked:before {
    opacity: 0;
}

.courses-list .courses-list-item .like-icon:after {
    content: "\f004";
    opacity: 0;
    transform: scale(0.5);
}

.courses-list .courses-list-item .like-icon.liked:after {
    opacity: 1;
    transform: scale(0.95);
}

.courses-list .courses-list-item .like-icon>input {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}



.jobapply {
    position: absolute;
    bottom: 0;
    right: 15px;
    background-color: #fff;
    padding: 2px 7px 0px;
    color: #222;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.jobapply:hover,
.jobapply:focus,
.jobapply:active {
    color: #000;
}

.job-box {
    position: relative;
    padding: 10px;
}

.job-box p {
    /*letter-spacing: 0.05em; */
    /*line-height: 22px; */
    font-size: 14px;
}

/*Start Phone a friend slider CSS */

.es-carousel-wrapper {
    border-radius: 0px;
}

.es-carousel-wrapper {
    border-radius: 0px !important;
}

/* End Phone a friend slider CSS */

.rg-image-wrapper {
    border-radius: 0px !important;
}



/* Multiselect Custom CSS start */
.form-group-default-multiselect {
    overflow: unset;
}


.form-group-default-multiselect .multiselect-native-select button {
    width: 100%;
    text-align: left;
    border: none;
    padding: 0 5px 5px;
    font-size: 13px;
    font-family: inherit;
    color: #000;
    opacity: .9;
    background-color: transparent;
}

.form-group-default-multiselect .multiselect-native-select .btn-group,
.form-group-default-multiselect .multiselect-native-select ul {
    width: 100%;
}

.form-group-default-multiselect .multiselect-native-select ul {
    background-color: #fff;
    padding: 10px;
}

.form-group-default-multiselect .multiselect-native-select ul .checkbox input[type=checkbox],
.multiselect-group input[type=checkbox] {
    opacity: 1;
    width: auto;
    position: unset;
    height: auto;
    vertical-align: middle;
    margin-bottom: 2px !important;
}

.form-group-default-multiselect .multiselect-native-select ul .multiselect-all label {
    padding: 5px 20px !important;
}

.form-group-default-multiselect .multiselect-native-select ul.dropdown-menu>li {
    padding: 5px 0;
}

.form-group-default-multiselect .multiselect-native-select ul .multiselect-item.multiselect-group {
    margin-top: 10px;
}

.form-group-default-multiselect .multiselect-native-select ul.dropdown-menu>li>a {
    line-height: 0;
}

.form-group-default-multiselect .multiselect-native-select ul label {
    font: inherit;
    opacity: 1;
    text-transform: none;
    vertical-align: middle;
    display: inline-block;
    line-height: normal;
}

.form-group-default-multiselect .multiselect-native-select .caret-container>.caret {
    border-top: 4px solid;
}

/* Multiselect Custom CSS start */

/* Footer CSS Starts */

/* .page-container .page-content-wrapper .footer {
        position: relative;
        display: -webkit-inline-box;
        left: 0;
        right: 0;
        bottom: 0;
        -webkit-transition: left .3s ease;
        transition: left .3s ease;
        } */

/* Footer CSS Ends here */


/* Noticeboard CSS Starts here */

.notice .ellipsis i {
    font-size: 16px;
}

.notice .card-title-X {
    width: 22px;
}

.notice .active {
    border: 1px solid black;
    padding: 5px;
}

.notice .card-header a {
    opacity: 0.8;
}

.notice .dropdown-toggle:after {
    display: none;
}

.notice .dropdown-menu {
    font-size: 14px;
    background-color: #f4f4f4;
    min-width: 50px;
}

.notice-message {
    word-break: break-word;
}

.notice-message * {
    max-width: 100% !important;
}

.notice.unpublished {
    opacity: 0.5;
}

.notice-message iframe {
    height: auto;
}

/* Noticeboard CSS Ends here */


.webinar-edit {
    float: right;
    top: 6px;
    position: relative;
    z-index: 2;
    right: 14px;
}

.webinar-delete {
    float: right;
    top: 5px;
    position: relative;
    z-index: 2;
    right: 16px;
}

/* Blockquotes CSS Starts here */

/* Some vars */

/* 1st */
.blockquote1 {
    position: relative;
    font-family: "Sanchez", serif;
    font-size: 2.4em;
    line-height: 1.5em;
    font-style: italic;
}

.blockquote1:before {
    content: "\201C";
    position: absolute;
    top: 0.25em;
    left: -0.15em;
    color: #e6e6e6;
    font-size: 6em;
    z-index: -1;
}

/* 2nd */
.blockquote2 {
    position: relative;
    padding-left: 1em;
    border-left: 0.2em solid #4d91b3;
    font-family: "Roboto", serif;
    font-size: 2.4em;
    line-height: 1.5em;
    font-weight: 100;
}

.blockquote2:before,
.blockquote2:after {
    content: "\201C";
    font-family: "Sanchez";
    color: #4d91b3;
}

.blockquote2:after {
    content: "\201D";
}

/* 3rd */
.blockquote3 {
    position: relative;
    font-family: "Sanchez", serif;
    font-size: 2.4em;
    line-height: 1.5em;
}

.blockquote3 footer {
    font-family: "Noto Sans", sans-serif;
    font-size: 0.6em;
    font-weight: 700;
    color: #d1d1d1;
    float: right;
}

.blockquote3 footer:before {
    content: "\2015";
}

.blockquote3:after {
    content: "\201D";
    position: absolute;
    top: 0.28em;
    right: 0;
    font-size: 6em;
    font-style: italic;
    color: #e6e6e6;
    z-index: -1;
}

blockquote {
    margin-bottom: 3em;
}

/* 4th */
.blockquote4 {
    margin: 20px auto;
    padding: 20px;
    position: relative;
    width: 300px;
}

.blockquote4 p {
    margin: 0;
    padding: 0;
}

.blockquote4 blockquote {
    margin: 0;
    padding: 0;
    position: relative;
}

.blockquote4 cite {
    font-style: normal;
}

.mb-style-2 blockquote {
    padding-top: 150px;
}

.mb-style-2 blockquote:after {
    background: none repeat scroll 0 0 rgba(235, 150, 108, 0.8);
    border-radius: 50% 50% 50% 50%;
    color: rgba(255, 255, 255, 0.5);
    content: "❞";
    font-family: 'icons';
    font-size: 70px;
    height: 130px;
    left: 50%;
    line-height: 130px;
    margin-left: -65px;
    position: absolute;
    text-align: center;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.1);
    top: 0;
    width: 130px;
}

.mb-style-2 blockquote:before {
    border-left: 5px solid rgba(235, 150, 108, 0.1);
    border-radius: 50% 50% 50% 50%;
    content: "";
    height: 500px;
    left: -50px;
    position: absolute;
    top: 0;
    width: 500px;
    z-index: -1;
}

.mb-style-2 blockquote p {
    background: none repeat scroll 0 0 rgba(255, 255, 255, 0.5);
    box-shadow: 0 -6px 0 rgba(235, 150, 108, 0.2);
    color: rgba(235, 150, 108, 0.8);
    display: inline;
    font-family: Baskerville, Georgia, serif;
    font-style: italic;
    font-size: 28px;
    line-height: 46px;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

.mb-attribution {
    text-align: right;
}

.mb-author {
    color: #D48158;
    font-size: 18px;
    font-weight: bold;
    padding-top: 10px;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.1);
    text-transform: uppercase;
}

cite a {
    color: #D7AA94;
    font-style: italic;
}

cite a:hover {
    color: #D48158;
}

/* 5th */
.blockquote5 {
    padding: 60px 80px 40px;
    position: relative;
}

.blockquote5 p {
    font-family: "Utopia-italic";
    font-size: 35px;
    font-weight: 700px;
    text-align: center;
}

.blockquote5:before {
    position: absolute;
    font-family: 'FontAwesome';
    top: 0;

    content: "\f10d";
    font-size: 200px;
    color: rgba(0, 0, 0, 0.1);

}

.blockquote5::after {
    content: "";
    top: 20px;
    left: 50%;
    margin-left: -100px;
    position: absolute;
    border-bottom: 3px solid #bf0024;
    height: 3px;
    width: 200px;
}

/* 6th */

.blockquote6 {
    font-size: 1.4em;
    width: 60%;
    margin: 50px auto;
    font-family: Open Sans;
    font-style: italic;
    color: #555555;
    padding: 1.2em 30px 1.2em 75px;
    border-left: 8px solid #78C0A8;
    line-height: 1.6;
    position: relative;
    background: #EDEDED;
}

.blockquote6::before {
    font-family: Arial;
    content: "\201C";
    color: #78C0A8;
    font-size: 4em;
    position: absolute;
    left: 10px;
    top: -10px;
}

.blockquote6::after {
    content: '';
}

.blockquote6 span {
    display: block;
    color: #333333;
    font-style: normal;
    font-weight: bold;
    margin-top: 1em;
}

/* 7th */

/* Blockquotes CSS Ends here */

p.premium-text,
p.standard-text {
    line-height: 24px;
}

.modal-xl {
    max-width: 1200px;
}

@media (min-width: 1368px) {
    .modal .modal-xl {
        width: 1200px;
    }
}

.btn-lime-vertical,
.btn-lime-vertical:focus {
    z-index: 1 !important;
    font-family: 'Oswald';
    text-transform: uppercase;
    font-size: 14px;
    color: #000;
    background-color: #daff16 !important;
    letter-spacing: 3px;
    font-weight: bold;
    transform: rotate(270deg);
    padding: 10px 15px !important;
    text-shadow: none;
}

.btn-lime-vertical:hover,
.btn-lime-vertical:active {
    color: #000;
    background-color: #d0f518;
}

.close.btn-lime-vertical {
    width: 121px;
}

.pac-container {
    z-index: 2000;
    /* to show google location dropdown over bs modal */
    overflow: visible;
}


.custom-side-modal .text-black * {
    color: #000;
}

.foryou-btn {
    position: fixed;
    right: 21px;
    top: 200px;
    transform-origin: right;
}

.custom-side-modal .modal-content {
    border: none;
}

.custom-side-modal .modal-body {
    background-color: #dedac9 !important;
}

.custom-side-modal .close {
    left: -17px;
    top: 102px !important;
    transform-origin: left;
    opacity: 1;
}

.custom-side-modal .modal-header {
    background-color: #daff16;
    padding-bottom: 15px;
}

.custom-side-modal .location-edit-icon,
.custom-side-modal .industry-edit-icon {
    background: #fff;
    padding: 4px;
    border-radius: 50%;
    cursor: pointer;
}

.custom-side-modal .location-edit-input,
.custom-side-modal .industry-edit-input {
    display: inline-block;
    background: transparent;
    border: none;
    border-bottom: 2px solid #000;
    width: 350px;
    max-width: 86%;
}

.custom-side-modal .industry-edit-input .select2-container .select2-selection {
    border: none !important;
    padding: 0;
    height: auto;
}


.custom-side-modal nav {
    background-color: #000;
    color: #fff;
    padding: 10px 20px;
    font-size: 14px;
}

.custom-side-modal nav * {
    display: inline-block;

}

.custom-side-modal nav>ul>li {
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    padding: 0 10px;
    cursor: pointer;
}

.custom-side-modal nav>ul>li>a,
.custom-side-modal nav>ul>li>a:hover {
    color: #fff;
}

.custom-side-modal section {
    padding: 25px;
}

.custom-side-modal .map {
    height: 470px;
    padding: 10px;
}

.custom-side-modal #sectionForYouJobs {
    background-color: #84143a;
}

.rust-red {
    color: #f03d29;
}

.custom-side-modal #sectionForYouContent {
    background-color: #daff16;
}

.custom-side-modal .section-heading {
    text-transform: uppercase;
    font-weight: bold;
}

.custom-side-modal .companies-view {
    font-weight: bold;
}

.custom-side-modal .companies-view span {
    text-transform: uppercase;
    font-size: 13px;
    cursor: pointer;
    font-weight: normal;
}

.custom-side-modal .companies-view span.active,
.custom-side-modal .companies-view span:hover {
    font-weight: bold;
}

.custom-side-modal .companies-content {
    background-color: #fff;
    margin: 0 -20px;
}

.custom-side-modal .list-view {
    padding: 40px 30px;
}

.custom-side-modal .companies-list {
    font-size: 14px;
    color: #000;
    line-height: normal;
    margin-bottom: 36px;
}

.custom-side-modal .accordian-btn {
    background: no-repeat;
    border: none;
    font-family: 'Roboto';
    font-size: 13px;
    text-transform: uppercase;
    font-weight: bold;
    cursor: pointer;
}

.custom-side-modal .accordian-btn[aria-expanded=true]>span:before {
    content: "Collapse \2212";
}

.custom-side-modal .accordian-btn[aria-expanded=false]>span:before {
    content: "Expand +";
}

.custom-side-modal ul.profile-cards,
.custom-side-modal .jobs-tile {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px 0;
    margin: 0;
}

#relatedSliderBtns>button,
#contentSliderBtns>button {
    background-color: transparent;
    border: 2px solid #000;
    border-radius: 50%;
    font-weight: bold;
    width: 30px;
    height: 30px;
    margin: 0 3px;
    cursor: pointer;
}

#relatedSliderBtns>button:hover {
    background-color: #000;
    color: #daff16;
}

#relatedSliderBtns>button:hover {
    background-color: #000;
    color: #f0f0f0;
}

#relatedSliderBtns>button.slick-disabled,
#contentSliderBtns>button.slick-disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* **** MEDIA QUERY **** */

@media (min-width: 768px) {
    .full-height-md-up {
        height: 100% !important;
    }

    .text-right-md-up {
        text-align: right !important;
    }
}

@media (max-width: 991px) {
    body.menu-behind .header .brand>img {
        width: 35%;
    }

    .modal .modal-xl {
        width: 700px;
    }

    .breadcrumb {
        padding: 20px 0 15px;
    }

    .breadcrumb>.breadcrumb-item+.breadcrumb-item:before {
        padding: 0;
    }

    .breadcrumb>.breadcrumb-item>a,
    .breadcrumb>.breadcrumb-item,
    .breadcrumb>.breadcrumb-item.active,
    .breadcrumb>.breadcrumb-item+.breadcrumb-item:before {
        letter-spacing: 0.5px;
    }

    .breadcrumb>.breadcrumb-item.active:before {
        padding-right: 5px;
    }

    .toggle-breadcrumb+div>.breadcrumb {
        padding: 0;
    }

    .column-reverse-md-down {
        flex-direction: column-reverse;
    }
}

@media (max-width: 767px) {

    .container-fluid,
    .jumbotron {
        padding-left: 15px;
        padding-right: 15px;
    }

    #fsw {
        vertical-align: top !important;
    }

    .full-height-sm {
        height: 100% !important;
    }

    .blue-cross {
        font-size: 16px;
    }

    /* .card-title.custom-card-title {
            padding: 2px 10px !important;
            margin-left: 8px !important;
            font-size: 12px !important;
            } */

    .btn-sm-compact {
        padding-bottom: 5px !important;
        padding-top: 5px !important;
        letter-spacing: 1.5px !important;
        font-size: 10px !important;
        min-width: 90px !important;
    }

    .custom-orange-tabs>li,
    .custom-black-tabs>li {
        min-width: 150px;
    }

    .text-center-sm-down {
        text-align: center !important;
    }

    .modal .modal-xl {
        max-width: 500px;
        width: 90%;
    }

    .foryou-btn {
        right: 17px;
        padding: 5px 10px;
        letter-spacing: 2px;
    }

    .custom-side-modal .accordian-btn[aria-expanded=true]>span:before {
        content: "\2212";
    }

    .custom-side-modal .accordian-btn[aria-expanded=false]>span:before {
        content: "+";
    }
}

@media (max-width: 575px) {

    body.menu-behind .header .brand>img {
        width: 50%;
    }

    .fc-toolbar .fc-left,
    .fc-toolbar .fc-right {
        float: none;
        display: inline-block;
        padding: 0 5px;
    }

    .text-center-xs {
        text-align: center !important;
    }

    .btn-xs-compact {
        padding-bottom: 5px !important;
        padding-top: 5px !important;
        letter-spacing: 1.5px !important;
        font-size: 10px !important;
        min-width: 90px !important;
    }

    .form-group-default.required:after {
        font-size: 13px;
    }

    .template-tiles {
        display: block;
    }

    .profile-cards:not(.umayalsolike-tiles) {
        text-align: center;
    }

    .profile-cards.umayalsolike-tiles>li {
        width: 165px;
    }

    .profile-cards.umayalsolike-tiles .percentage {
        height: 155px;
        line-height: 155px;
    }
}
