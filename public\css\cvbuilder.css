/* < page css> */

.cv-tiles {
    margin: 0 -10px;
}

.cv-tiles>li {
    background-color: #eee;
    padding: 0;
    height: 140px;
    width: 130px;
    position: relative;
    margin: 0 10px 30px;
}

.cv-tiles>li .cv-tile-content {
    position: absolute;
    width: 100%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 0 10px;
}

.cv-tiles>.create-new-tile .cv-tile-content>div:first-child {
    background-color: #0A0AFD;
    color: #fff;
    font-size: 31px;
    width: 35px;
    height: 35px;
    line-height: 35px;
    border-radius: 50%;
    margin: 0 auto;
}

.cv-tile-content .title {
    color: #000;
    font-family: 'Roboto';
    font-size: 13px;
    margin-top: 5px;
}

.cv-tiles>li:not(.create-new-tile) .cv-tile-content>.icons {
    padding: 5px 0;
}

.cv-tiles>li:not(.create-new-tile) .cv-tile-content>.icons>a {
    display: inline-block;
    background-color: #000;
    color: #fff;
    width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 50%;
    font-size: 11px;
    margin: 0 2px;
}

/* </ List page css> */


/* < template page css> */


.custom-check-radio label {
    padding: 0;
    font-weight: bold;
}

.custom-check-radio .form-check-inline {
    padding: 0 3vw;
    margin: 0;
}


.custom-check-radio input[type=radio] {
    width: 0px;
    height: 0px;
    margin: 0;
    position: absolute !important;
    right: -5px;
}

.custom-check-radio input[type=radio]:checked:after {
    font-family: 'FontAwesome';
    content: "\f00c";
    height: 10px;
    width: 10px;
}

.cv-template-tiles {
    margin-left: -15px;
    margin-right: -15px;
    /* text-align: center; */
}


/* .card-input-label {
    width: 100%;
    margin-bottom: 30px;
} */

.card-input-label {
    width: 265px;
    height: 313px;
    margin-bottom: 30px;
    padding: 0px 15px;
}

.card-input-element {
    display: none;
}

.card-input {
    /* height: 400px; */
    height: 100%;
    background-color: #cfcfcf;
    background-size: 100% 100%;
    background-position: center;
    position: relative;
    border: 1px solid #888;
    overflow: hidden;
    cursor: pointer;
    transition: all .2s ease;
}

.card-input:hover {
    background-size: 110% 110%;
}

.card-input .thumbnail {
    width: 100%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.card-input-element:checked+.card-input {
    border: 1px solid #000;
    /* box-shadow: 3px 3px 13px 2px #bbb; */
}

.card-input-element:checked+.card-input:after {
    background: #000;
    color: #fff;
    padding: 3px;
    content: "\f00c";
    font-family: 'FontAwesome';
    font-size: 20px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

/* </ Select template page css> */

/* < builder page css> */


#preview {
    border: 1px solid #bbb;
    overflow: hidden;
    overflow-y: auto;
    height: 500px;
    position: relative;
}

#preview>canvas+canvas {
    border-top: 1px solid #bbb;
}

.sorting-handle {
    position: absolute;
    left: 3px;
    top: 3px;
    color: #9c9c9c !important;
    z-index: 4;
}

.cke_wysiwyg_div {
    padding: 20px !important;
}

.cke .cke_editable * {
    font-size: 13px;
    line-height: 1.6;
    color: #333;
    word-wrap: break-word;
}

.card-cvbuilder * {
    font-size: 11px;
    letter-spacing: 0.03em;
    line-height: normal;
    color: #3c3c3c;
}

.card-cvbuilder>.card-header {
    padding: 10px 10px 5px 15px;
    min-height: auto;
}

.card-cvbuilder>.card-header .card-title {
    font-family: 'Roboto', sans-serif;
    font-size: 13px;
    color: #000;
    font-weight: bold;
    letter-spacing: 0.5px;
    padding-top: 5px;
    text-transform: none;
}

.card-cvbuilder>.card-header .card-controls>.btn {
    background-color: #000;
    width: 18px;
    height: 18px;
    text-align: center;
    border-radius: 50%;
    padding: 0;
}

.card-cvbuilder>.card-header .card-controls>.btn>i {
    color: #fff;
    line-height: 18px;
    font-size: 10px;
}

.card-cvbuilder>.card-block {
    padding-top: 0
}


.card-cvbuilder .card-footer {
    padding: 0px 25px 25px;
    min-height: auto;
    background-color: inherit;
    border-top: none;
}

.card-cvbuilder>.card-header .card-controls>.btn+.btn {
    margin-left: 4px;
}

.btn.btn-link.btn-add {
    font-family: 'Roboto', sans-serif !important;
    font-size: 12px;
    font-weight: bold;
    padding: 0;
}

.btn.btn-link.btn-add:hover,
.btn.btn-link.btn-add:active {
    color: #000;
}

.tips-accordion .tips-accordion-header a {
    padding: 0 !important;
    background-color: transparent;
    width: auto;
    color: #0A0AFD;
    font-size: 12px;
    font-weight: bold;
    letter-spacing: 1.5px;
}

.tips-accordion .tips-accordion-header a:after {
    content: "";
    font-family: "Glyphicons Halflings";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: transform 0.25s linear;
    -webkit-transition: -webkit-transform 0.25s linear;
    -moz-transition: -moz-transform 0.25s linear;
    -o-transition: -o-transform 0.25s linear;
    position: relative;
    bottom: 2px;
}

.tips-accordion .tips-accordion-header a[aria-expanded="true"]:after {
    content: "\2212";
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

.tips-accordion .tips-accordion-header a[aria-expanded="false"]:after {
    content: "\002b";
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
}

.card-cvbuilder textarea {
    color: #000;
    resize: none;
    border-color: #888;
    border-radius: inherit;
    background-color: #fff;
}

.card-cvbuilder textarea:focus {
    background-color: #fff;
    border-color: #000;
}

/* .cv-btn-group {
    margin-left: -2px;
    margin-right: -2px;
} */

.btn.cv-btn-black {
    font-family: "Roboto", sans-serif;
    color: #fff;
    background-color: #000;
    border: none;
    border-radius: 0;
    padding: 7px 10px;
    min-width: 90px;
    margin-top: 10px;
}

.btn.cv-btn-black:hover:not(.disabled),
.btn.cv-btn-black:focus:not(.disabled),
.btn.cv-btn-black:active:not(.disabled) {
    border: none;
    outline: 1px solid #000 !important;
    outline-offset: -1px;
    background-color: #fff;
    color: #000;
}

.cv-btn-black:not(:last-child) {
    margin-right: 8px;
}

.cv-textarea-group,
.cv-input-group {
    position: relative;
}

.cv-input-group {
    display: flex;
    display: -webkit-flex;
    display: -ms-flex;
    display: -moz-flex;
    display: -o-flex;
    height: 100%;
    border-bottom: 1px solid #888;
    align-self: center;
}

.cv-input-group+.cv-input-group {
    margin-top: 12px;
}

.cv-input-group.focused {
    border-color: #000;
}

.cv-input-group>input.form-control[type=number]::-webkit-outer-spin-button,
.cv-input-group>input.form-control[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
/* .cv-input-group>input.form-controlinput[type=number] {
  -moz-appearance:textfield;
} */


.cv-input-group>label:not(.error) {
    font-family: "Roboto", sans-serif;
    margin-bottom: 0;
    font-size: 10px;
    align-self: center;
}

.cv-input-group>.form-control {
    height: 25px !important;
    min-height: 25px;
    width: auto;
    border: none;
    color: #000;
    font-size: 12px;
    padding: 0 20px 0 15px;
    background: transparent;
    flex-grow: 100;
    -webkit-flex-grow: 100;
    -ms-flex-grow: 100;
    -moz-flex-grow: 100;
    -o-flex-grow: 100;
}

.cv-input-inline-group>.form-control {
    width: 100%;
    padding-right: 0px;
    padding-left: 8px;
}

.cv-input-inline-group>.input-group-addon {
    padding: 0;
    background-color: transparent;
    border: none;
}


.cv-input-group.textarea-group>label {
    align-self: unset;
    padding-top: 6px;
}

.cv-input-group.textarea-group>textarea.form-control {
    padding-top: 6px;
}

.cv-input-group.textarea-group>textarea.form-control:focus {
    background-color: transparent;
}

.two-addons {
    padding-right: 32px !important;
}


.cv-field-addon {
    position: absolute;
    right: 7px;
    bottom: 5px;
}

[data-input=other] .cv-field-addon {
    bottom: 2px;
}

.cv-field-addon>i {
    color: #000;
    font-size: 12px;
    cursor: pointer;
}

.cv-checkbox.checkbox label {
    min-width: 15px;
    min-height: 15px;
    margin-right: 0;
    padding-left: 0;
    vertical-align: middle;
}

.cv-checkbox.checkbox label::before {
    top: 0;
    border-color: #888
}

.cv-checkbox.checkbox label::after {
    left: 3px;
    top: -3px;
    font-size: 10px;
}


.cv-checkbox.checkbox label:before,
.cv-checkbox.checkbox label::after {
    width: 15px;
    height: 15px;
    border-radius: 0;
    margin-right: 0;
}

.cv-checkbox.checkbox input[type=checkbox]:focus+label:before {
    background-color: #fff;
    border-color: #000;
}

.cv-checkbox.checkbox input[type=checkbox]:checked+label:before {
    border-width: 7.5px;
}

.education-group,
.reference-group,
.proexp-group {
    margin-bottom: 10px;
}

.education-group+.education-group,
.reference-group+.reference-group,
.proexp-group+.proexp-group {
    padding-top: 20px;
}

hr.separator {
    margin-top: 4px;
    border-color: #888;
}

.addSection-btn>div:first-child {
    background-color: #0A0AFD;
    color: #fff;
    font-size: 36px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    margin: 0 auto;
}

.addSection-btn>div:last-child {
    color: #000;
    font-family: 'Roboto';
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
    margin-top: 5px;
}

.addSection-btn:hover>div:first-child {
    background-color: #0000df;
}

.addSection-btn:hover>div:last-child {
    color: #000;
}

.addsection-tiles-group {
    margin-left: -10px;
    margin-right: -10px;
    display: flex;
    display: -ms-flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -o-flex;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    -moz-flex-wrap: wrap;
    -o-flex-wrap: wrap;
}


.card.addsection-tile {
    width: 240px;
    display: inline-block;
    padding: 0;
    margin-left: 10px;
    margin-right: 10px;
    margin-bottom: 20px;
    border: none;
    border-radius: 7px;
    box-shadow: 0px 3px 7px 0px #bbb;
}

.card.addsection-tile:hover {
    box-shadow: 0px 5px 8px 0px #999;
    cursor: pointer;
}

.addsection-tile-heading {
    color: #222;
    font-weight: bold;
    margin: 10px 0 5px;
    display: flex;
    display: -webkit-flex;
    display: -ms-flex;
    display: -moz-flex;
    display: -o-flex;
    align-items: center;
    -webkit-align-items: center;
    -ms-align-items: center;
    -moz-align-items: center;
    -o-align-items: center;
}

.addsection-tile-heading>img {
    width: 15px;
    margin-right: 10px;
}

.addsection-tile-heading>span {
    line-height: 14px;
}

.addsection-tile-desc {
    line-height: 17px;
    font-size: 11px;
}

/* </ cv builder page css> */

@media (min-width: 1200px) {
    #modalAddSection .modal-lg {
        max-width: 1090px;
        width: 1090px;
    }
}

@media (min-width: 768px) {
    #templateSelect form .row {
        margin-left: -15px;
        margin-right: -15px;
    }

    #templateSelect form .row [class*=col-] {
        padding-right: 15px !important;
        padding-left: 15px !important;
    }
}

@media (max-width: 575px) {
    .custom-check-radio .form-check-inline {
        display: block;
        padding: 0;
    }
}
.downloadas{
    display: inline;
}
.downloadas .dropdown-toggle:after{
    right:3px;
}
.downloadas .dropdown-menu{
    width: 100%;
}
.btn-primary.hover, .btn-primary:hover, .show .dropdown-toggle.btn-primary{
    background-color:#fd3806 !important;
    color:#fff;
}