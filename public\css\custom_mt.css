@import url('https://fonts.googleapis.com/css2?family=Inter&display=swap');


body {
    overflow-x: hidden;
}

.app-aside {
    background-color: #000;
}

.flex-column {
    flex-direction: column !important;
}

.flex-column-fluid {
    flex: 1 0 auto;
}


.hover-scroll-overlay-y {
    position: relative;
}


@media only screen and (min-width: 980px) {

    body.menu-pin .page-sidebar {
        width: 140px;
    }

    body.menu-pin .page-container .page-content-wrapper .content {
        padding-left: 140px;
    }

    body.menu-pin .page-container .page-content-wrapper .footer {
        left: 140px;
    }
}



.menu-item {
    display: block;
    padding: 0.15rem 0;
}


.menu-item .menu-link {
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 0;
    flex: 0 0 100%;
    padding: 0.65rem 1rem;
    transition: none;
    outline: none !important;
    font-family: Inter, Helvetica, "sans-serif" !important;
    font-size: 13px !important;
    letter-spacing: 0px;
}


.menu-item .menu-link .menu-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    margin-right: 0.5rem;
}

.menu-sub-dropdown {
    display: none;
    /* border-radius: 0.475rem; */
    background-color: #fff;
    box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
    z-index: 107;
    min-width: 250px;
}

.menu-sub-dropdown .menu-item>.menu-link {
    padding: 0.3rem 1rem;
}

.menu-sub-dropdown .menu-item>.menu-link>.menu-link {
    padding: 0;
}

.menu-title-gray-700 .menu-item .menu-link .menu-title {
    color: #5e6278;
    font-weight: 500 !important;
}

.hover-scroll-overlay-y {
    position: relative;
}

.menu-sub {
    display: none;
    padding: 0;
    margin: 0;
    list-style: none;
    flex-direction: column;
}

.show.menu-dropdown>.menu-sub-dropdown,
.menu-sub-dropdown.menu.show,
.menu-sub-dropdown.show[data-popper-placement] {
    display: flex;
    will-change: transform;
    animation: menu-sub-dropdown-animation-fade-in 0.3s ease 1, menu-sub-dropdown-animation-move-up 0.3s ease 1;
}

.aside-menu .menu>.menu-item>.menu-link .menu-title {
    color: #707070;
}

.py-8 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
}

.py-5 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
}

.fw-semibold {
    font-weight: 500 !important;
}

.aside-menu {
    height: 100%;
    margin: 0px;
    padding: 0px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 13px !important;
    font-weight: 400;
    font-family: 'Inter', sans-serif !important;

}

.py-20 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
}

.aside-menu .menu>.menu-item>.menu-link .menu-icon,
.aside-menu .menu>.menu-item>.menu-link .menu-icon .svg-icon,
.aside-menu .menu>.menu-item>.menu-link .menu-icon i {
    color: #3a3a5d;
}

.menu>.menu-item>.menu-link .menu-icon .svg-icon {
    color: grey !important;
}

.menu-item .menu-link .menu-icon .svg-icon {
    line-height: 1;
}

.aside-menu .menu>.menu-item>.menu-link .menu-icon .svg-icon>svg {
    padding: 2px 0;
}

.svg-icon {
    line-height: 1;
    color: var(--kt-text-muted);
}

.page-sidebar.myNav {
    overflow: visible;
}