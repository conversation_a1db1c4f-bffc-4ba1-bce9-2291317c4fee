.bg-orange {
    background-color: #fd3806;
}

.overflow-hidden {
    overflow: hidden;
}

.btn-steps {
    max-width: 270px;
}

.jumbotron {
    margin-bottom: 0px;
}

.trait {
    padding-top: 40px;
}

.trait .title {
    font-weight: bold;
    letter-spacing: 1.1px;
    font-size: 12px;
    word-break: break-word;
}

.trait .description {
    font-size: 14px;
}

.row.step-3 {
    background-color: #0000FF;
}

.eye-3 {
    background-color: #eaaa69;
}

.step-3 .text-desc h4 {
    background-color: #0000FF;
}

/* jTinder styling */

.tinderslide {
    width: 100%;
    min-height: 300px;
    background-color: transparent;
}

.tinderslide ul {
    margin: 0 auto;
    position: static;
    max-width: 290px;
    height: 350px;
    /* border: 1px solid #eee; */
    /* background-color: #DADCDB; */
}

.tinderslide li {
    display: flex;
    max-width: inherit;
    height: inherit;
    left: 0;
    right: 0;
    background: #DADCDB;
    z-index: 0;
    margin: auto;
    box-shadow: none;
    border: none;
}

.tinderslide li .count {
    position: absolute;
    top: 4px;
    right: 0;
    width: 100px;
    background-color: #000;
    height: 50px;
    color: #fff;
    font-size: 30px;
    line-height: 50px;
    font-family: 'Bebas New Regular';
}

.tinderslide li.shadow {
    box-shadow: none;
}

.tinder-actions {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    margin: auto;
    padding: 0 20px;
    max-width: 300px;
}

.tinder-actions a {
    left: 0;
    right: 0;
    position: static;
    border-radius: 0;
    box-shadow: none;
    margin: 0;
    display: inline-block;
    line-height: normal;
}

.tinder-actions a.dislike {
    float: left;
    left: 0;
    right: 0;
}

.tinder-actions a.like {
    float: right;
    left: 0;
    right: 0;
}

#skill-cards ul li .skill-name {
    color: #ffffff;
    padding: 5px;
}

.skill-name>span {
    box-shadow: 3px 0 0 7px #070af7, -3px 0 0 7px #070af7;
}

.skill-name>span>span {
    background-color: #070af7;
    position: relative;
    line-height: 33px;
}

/* #skill-cards .empty {
    position: absolute;
    line-height: 350px;
    top: 0;
    width: 290px;
} */

.tinder-actions a.dislike {
    opacity: 1 !important;
    text-align: right;
}

.tinder-actions a.like {
    opacity: 1 !important;
    text-align: left;
}

.tinder-actions a {
    width: 80px;
    height: 55px;
}

.tinder-actions a:hover,
.tinder-actions a:focus {
    background: transparent;
}

.tinder-actions a>img {
    width: 33px;
}

.tinder-actions a>div {
    display: block;
    background-color: #000;
    color: #fff;
    font-size: 22px;
    padding: 5px 0 3px;
    text-align: center;
    margin-right: 0;
    width: 70px;
    font-family: 'Bebas New Regular';
}

#skill-cards .vertical-logo {
    position: relative;
    right: -290px;
    width: 12px;
    top: -2px;
}

/*   jTinder Styling End  */

.optimism label {
    display: block;
    /* border: solid 1px #FFBD75; */
    border: solid 1px #F4F4F4;
    line-height: 40px;
    height: 40px;
    width: 240px;
    -webkit-font-smoothing: antialiased;
    margin-top: 10px;
    font-family: Arial, Helvetica, sans-serif;
    color: #000;
    text-align: center;
    box-shadow: 7px 8px 0px 1px #000;
    /* background-color: #FFBD75; */
    background-color: #e2e2e2;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    padding: 0 10px;
}

.optimism input[type=checkbox] {
    display: none;
}

.optimism input:checked+label {
    border: solid 1px #000;
    color: #fff;
    background-color: #000;
    box-shadow: 7px 8px 0px 1px #e2e2e2;
}

.optimism input:checked+label:before {
    content: "\2713 ";
}

.check {
    visibility: hidden;
}

.optimism input:checked+label .check {
    visibility: visible;
}

.optimism input.checkbox:checked+label:before {
    content: "";
}

.row.optimism .col-md-3 {
    height: 70px;
    position: relative;
}

/* <intesest-section> */

.interest-card-row {
    max-width: 850px;
}

.interest-cards {
    background-color: #DADCDB;
    color: #fff;
    padding: 5px;
    height: 350px;
    /* max-width: 350px; */
    width: 265px !important;
    background-repeat: no-repeat;
    /* background-size: cover; */
    background-size: 100% 100%;
    position: relative;
}

.interest-cards .count {
    font-family: 'Bebas New Regular';
    font-size: 40px;
    line-height: 35px;
    letter-spacing: 2px;
}

.interest-cards .interestsU {
    font-family: 'Bebas New Regular';
    font-size: 20px;
    line-height: 13px;
    border-bottom: 1px solid #e8e8e8;
    display: inline;
    letter-spacing: 2px;
    text-shadow: 0px 0px 2px #777;
}

.interest-cards .bottom {
    position: absolute;
    max-width: inherit;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
}


.interest-cards .bottom .title {
    font-size: 13px;
    letter-spacing: 1px;
    padding: 0 10px 2px
}

.interest-cards .bottom .title>span {
    box-shadow: 0px 0 0 4px #070af7, 0px 0 0 4px #070af7;
}

.interest-cards .bottom .title>span>span {
    background-color: #070af7;
    position: relative;
    line-height: 28px;
}

.interest-cards .checkbox label {
    padding-left: 32px;
    min-width: 30px;
    min-height: 30px;
    line-height: 32px;
}

.interest-cards .checkbox input[type=checkbox]:checked+label:before {
    border-width: 15.5px;
}

.interest-cards .checkbox label:before {
    width: 30px;
    height: 30px;
    border: 1px solid #fff;
}

.interest-cards .checkbox input[type=checkbox]:focus+label:before {
    background-color: #fff;
}

.interest-cards .checkbox input[type=checkbox]+label::after {
    font-family: FontAwesome;
    content: "\F00C";
    color: #CCCCCC;
    font-size: 24px;
}

.interest-cards .checkbox input[type=checkbox]:checked+label::after {
    color: #000;
}

.interest-cards .checkbox label::after {
    width: 29px;
    height: 29px;
}

.interest-cards .checkbox input[type=checkbox]+label:before {
    border-width: 15.5px;
}

/* </intesest-section> */

ul.custom-step-tabs {
    display: block;
}

ul.nav-tabs.custom-step-tabs>li>a {
    min-width: auto;
    padding: 6px 8px;
}

ul.nav-tabs.custom-step-tabs .nav-link[data-toggle=tab] {
    color: #000;
    cursor: pointer;
}

ul.nav-tabs.custom-step-tabs .nav-link {
    cursor: pointer;
    color: #333;
    border: 2px solid transparent;
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    text-align: center;
    font-size: 16px;
    font-family: 'Bebas New Regular';
}

.cursor-not-allowed {
    cursor: not-allowed !important;
}

ul.nav-tabs.custom-step-tabs .nav-link.active {
    color: #fff;
    background-color: #000;
    border-color: #000;
    font-weight: 900;
    font-size: 16px
}

ul.nav.nav-tabs.custom-step-tabs {
    position: fixed;
    border: 2px solid #000;
    z-index: 9;
    top: 35%;
    width: 34px;
    right: 40px;
    background-color: rgba(255, 255, 255, 0.5);
}

@font-face {
    font-family: 'icomoon';
    src: url('../fonts/icomoon.eot?gaq9ez');
    src: url('../fonts/icomoon.eot?gaq9ez#iefix') format('embedded-opentype'), url('../fonts/icomoon.ttf?gaq9ez') format('truetype'), url('../fonts/icomoon.woff?gaq9ez') format('woff'), url('../fonts/icomoon.svg?gaq9ez#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
}

[class^="icon-"],
[class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-Chilly-Individual-Empty:before {
    content: "\e900";
}

.icon-Chilly-Individual-Red:before {
    content: "\e901";
}

.icon-Chilly-Individual-Yellow:before {
    content: "\e902";
}

.final-step {
    padding-top: 15px !important;
    height: 100px;
}

.final-step .btn-steps {
    margin-top: 11px;
    max-width: 370px;
    transition: all .2s ease-in-out !important;
    padding: 0;
}

.final-step .btn-steps:hover {
    transform: scale(1.1);
}

.percentage-parallex {
    padding: 20vw 0;
    background-attachment: fixed;
    background-position: center bottom;
    background-repeat: no-repeat;
    background-size: 100%;
}

.step-one-img-parallex {
    background-image: url(../images/step-one-image.jpeg);
    padding: 30vw 0;
}

#form-scoring {
    width: 100%;
}

.swipe .empty {
    padding-top: 116px;
}

.checkbox label:before {
    border-radius: 0px;
}


@media (min-width: 768px) {
    form .row {
        margin-left: -15px;
        margin-right: -15px;
    }

    form .row [class*=col-]:first-child,
    form .row [class*=col-]:last-child,
    form .row [class*=col-]:not(:first-child),
    form .row [class*=col-]:not(:last-child) {
        padding-left: 15px;
        padding-right: 15px;
    }

    #addNominee {
        display: none;
    }
}

@media (min-width: 1200px) {
    .interest-col-2 {
        top: 50px;
    }
}

@media (max-width: 1199px) {
    .interest-card-row {
        max-width: 570px;
    }
}

@media (max-width: 767px) {
    .invitation-col:not(:first-child) {
        display: none;
    }

    .percentage-parallex {
        background-position: center 220%;
    }
}

@media (max-width: 991px) {
    ul.nav.nav-tabs.custom-step-tabs {
        right: 20px;
    }
}

@media (max-width: 575px) {
    .percentage-parallex {
        background-attachment: scroll;
    }
}