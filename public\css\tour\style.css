@charset "UTF-8";
/*--------------------------------------------------------------
BASE WEB
--------------------------------------------------------------*/
/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
1.0 Normalize
2.0.Fonts
3.0 Variables
4.0 Mixins
5.0 Boostrap Grid
6.0 Accessibility
7.0 Alignments
8.0 Typography
9.0 Forms
10.0. Tables
11.0 Formatting
12.0 Lists
13.0 Links
14.0 Navigation
15.0 Comments
16.0 Widgets
17.0 Modules

--------------------------------------------------------------*/
/*--------------------------------------------------------------
1.0 Normalize
Styles based on Normalize v8.0.0 @link https://github.com/necolas/normalize.css
--------------------------------------------------------------*/
/*! normalize.css v8.0.0 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}

/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/* Text-level semantics
   ========================================================================== */
/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  /* 2 */
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none;
}

/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: 1.15;
  /* 1 */
  margin: 0;
  /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  /* 1 */
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box;
  /* 1 */
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/* Interactive
   ========================================================================== */
/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item;
}

/* Misc
   ========================================================================== */
/**
 * Add the correct display in IE 10+.
 */
template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none;
}

/*--------------------------------------------------------------
2.0 Fonts
--------------------------------------------------------------*/
/*!
 * Font Awesome Free 5.10.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
.fa,
.fas,
.far,
.fal,
.fad,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

.fa-lg {
  font-size: 1.33333em;
  line-height: 0.75em;
  vertical-align: -.0667em;
}

.fa-xs {
  font-size: .75em;
}

.fa-sm {
  font-size: .875em;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0;
}

.fa-ul > li {
  position: relative;
}

.fa-li {
  left: -2em;
  position: absolute;
  text-align: center;
  width: 2em;
  line-height: inherit;
}

.fa-border {
  border: solid 0.08em #eee;
  border-radius: .1em;
  padding: .2em .25em .15em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left,
.fas.fa-pull-left,
.far.fa-pull-left,
.fal.fa-pull-left,
.fab.fa-pull-left {
  margin-right: .3em;
}

.fa.fa-pull-right,
.fas.fa-pull-right,
.far.fa-pull-right,
.fal.fa-pull-right,
.fab.fa-pull-right {
  margin-left: .3em;
}

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
          animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
          animation: fa-spin 1s infinite steps(8);
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  transform: rotate(90deg);
}

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  transform: rotate(180deg);
}

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  transform: scale(1, -1);
}

.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  transform: scale(-1, -1);
}

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical,
:root .fa-flip-both {
  -webkit-filter: none;
          filter: none;
}

.fa-stack {
  display: inline-block;
  height: 2em;
  line-height: 2em;
  position: relative;
  vertical-align: middle;
  width: 2.5em;
}

.fa-stack-1x,
.fa-stack-2x {
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: #fff;
}

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
readers do not read off random characters that represent icons */
.fa-500px:before {
  content: "\f26e";
}

.fa-accessible-icon:before {
  content: "\f368";
}

.fa-accusoft:before {
  content: "\f369";
}

.fa-acquisitions-incorporated:before {
  content: "\f6af";
}

.fa-ad:before {
  content: "\f641";
}

.fa-address-book:before {
  content: "\f2b9";
}

.fa-address-card:before {
  content: "\f2bb";
}

.fa-adjust:before {
  content: "\f042";
}

.fa-adn:before {
  content: "\f170";
}

.fa-adobe:before {
  content: "\f778";
}

.fa-adversal:before {
  content: "\f36a";
}

.fa-affiliatetheme:before {
  content: "\f36b";
}

.fa-air-freshener:before {
  content: "\f5d0";
}

.fa-airbnb:before {
  content: "\f834";
}

.fa-algolia:before {
  content: "\f36c";
}

.fa-align-center:before {
  content: "\f037";
}

.fa-align-justify:before {
  content: "\f039";
}

.fa-align-left:before {
  content: "\f036";
}

.fa-align-right:before {
  content: "\f038";
}

.fa-alipay:before {
  content: "\f642";
}

.fa-allergies:before {
  content: "\f461";
}

.fa-amazon:before {
  content: "\f270";
}

.fa-amazon-pay:before {
  content: "\f42c";
}

.fa-ambulance:before {
  content: "\f0f9";
}

.fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

.fa-amilia:before {
  content: "\f36d";
}

.fa-anchor:before {
  content: "\f13d";
}

.fa-android:before {
  content: "\f17b";
}

.fa-angellist:before {
  content: "\f209";
}

.fa-angle-double-down:before {
  content: "\f103";
}

.fa-angle-double-left:before {
  content: "\f100";
}

.fa-angle-double-right:before {
  content: "\f101";
}

.fa-angle-double-up:before {
  content: "\f102";
}

.fa-angle-down:before {
  content: "\f107";
}

.fa-angle-left:before {
  content: "\f104";
}

.fa-angle-right:before {
  content: "\f105";
}

.fa-angle-up:before {
  content: "\f106";
}

.fa-angry:before {
  content: "\f556";
}

.fa-angrycreative:before {
  content: "\f36e";
}

.fa-angular:before {
  content: "\f420";
}

.fa-ankh:before {
  content: "\f644";
}

.fa-app-store:before {
  content: "\f36f";
}

.fa-app-store-ios:before {
  content: "\f370";
}

.fa-apper:before {
  content: "\f371";
}

.fa-apple:before {
  content: "\f179";
}

.fa-apple-alt:before {
  content: "\f5d1";
}

.fa-apple-pay:before {
  content: "\f415";
}

.fa-archive:before {
  content: "\f187";
}

.fa-archway:before {
  content: "\f557";
}

.fa-arrow-alt-circle-down:before {
  content: "\f358";
}

.fa-arrow-alt-circle-left:before {
  content: "\f359";
}

.fa-arrow-alt-circle-right:before {
  content: "\f35a";
}

.fa-arrow-alt-circle-up:before {
  content: "\f35b";
}

.fa-arrow-circle-down:before {
  content: "\f0ab";
}

.fa-arrow-circle-left:before {
  content: "\f0a8";
}

.fa-arrow-circle-right:before {
  content: "\f0a9";
}

.fa-arrow-circle-up:before {
  content: "\f0aa";
}

.fa-arrow-down:before {
  content: "\f063";
}

.fa-arrow-left:before {
  content: "\f060";
}

.fa-arrow-right:before {
  content: "\f061";
}

.fa-arrow-up:before {
  content: "\f062";
}

.fa-arrows-alt:before {
  content: "\f0b2";
}

.fa-arrows-alt-h:before {
  content: "\f337";
}

.fa-arrows-alt-v:before {
  content: "\f338";
}

.fa-artstation:before {
  content: "\f77a";
}

.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

.fa-asterisk:before {
  content: "\f069";
}

.fa-asymmetrik:before {
  content: "\f372";
}

.fa-at:before {
  content: "\f1fa";
}

.fa-atlas:before {
  content: "\f558";
}

.fa-atlassian:before {
  content: "\f77b";
}

.fa-atom:before {
  content: "\f5d2";
}

.fa-audible:before {
  content: "\f373";
}

.fa-audio-description:before {
  content: "\f29e";
}

.fa-autoprefixer:before {
  content: "\f41c";
}

.fa-avianex:before {
  content: "\f374";
}

.fa-aviato:before {
  content: "\f421";
}

.fa-award:before {
  content: "\f559";
}

.fa-aws:before {
  content: "\f375";
}

.fa-baby:before {
  content: "\f77c";
}

.fa-baby-carriage:before {
  content: "\f77d";
}

.fa-backspace:before {
  content: "\f55a";
}

.fa-backward:before {
  content: "\f04a";
}

.fa-bacon:before {
  content: "\f7e5";
}

.fa-balance-scale:before {
  content: "\f24e";
}

.fa-balance-scale-left:before {
  content: "\f515";
}

.fa-balance-scale-right:before {
  content: "\f516";
}

.fa-ban:before {
  content: "\f05e";
}

.fa-band-aid:before {
  content: "\f462";
}

.fa-bandcamp:before {
  content: "\f2d5";
}

.fa-barcode:before {
  content: "\f02a";
}

.fa-bars:before {
  content: "\f0c9";
}

.fa-baseball-ball:before {
  content: "\f433";
}

.fa-basketball-ball:before {
  content: "\f434";
}

.fa-bath:before {
  content: "\f2cd";
}

.fa-battery-empty:before {
  content: "\f244";
}

.fa-battery-full:before {
  content: "\f240";
}

.fa-battery-half:before {
  content: "\f242";
}

.fa-battery-quarter:before {
  content: "\f243";
}

.fa-battery-three-quarters:before {
  content: "\f241";
}

.fa-battle-net:before {
  content: "\f835";
}

.fa-bed:before {
  content: "\f236";
}

.fa-beer:before {
  content: "\f0fc";
}

.fa-behance:before {
  content: "\f1b4";
}

.fa-behance-square:before {
  content: "\f1b5";
}

.fa-bell:before {
  content: "\f0f3";
}

.fa-bell-slash:before {
  content: "\f1f6";
}

.fa-bezier-curve:before {
  content: "\f55b";
}

.fa-bible:before {
  content: "\f647";
}

.fa-bicycle:before {
  content: "\f206";
}

.fa-biking:before {
  content: "\f84a";
}

.fa-bimobject:before {
  content: "\f378";
}

.fa-binoculars:before {
  content: "\f1e5";
}

.fa-biohazard:before {
  content: "\f780";
}

.fa-birthday-cake:before {
  content: "\f1fd";
}

.fa-bitbucket:before {
  content: "\f171";
}

.fa-bitcoin:before {
  content: "\f379";
}

.fa-bity:before {
  content: "\f37a";
}

.fa-black-tie:before {
  content: "\f27e";
}

.fa-blackberry:before {
  content: "\f37b";
}

.fa-blender:before {
  content: "\f517";
}

.fa-blender-phone:before {
  content: "\f6b6";
}

.fa-blind:before {
  content: "\f29d";
}

.fa-blog:before {
  content: "\f781";
}

.fa-blogger:before {
  content: "\f37c";
}

.fa-blogger-b:before {
  content: "\f37d";
}

.fa-bluetooth:before {
  content: "\f293";
}

.fa-bluetooth-b:before {
  content: "\f294";
}

.fa-bold:before {
  content: "\f032";
}

.fa-bolt:before {
  content: "\f0e7";
}

.fa-bomb:before {
  content: "\f1e2";
}

.fa-bone:before {
  content: "\f5d7";
}

.fa-bong:before {
  content: "\f55c";
}

.fa-book:before {
  content: "\f02d";
}

.fa-book-dead:before {
  content: "\f6b7";
}

.fa-book-medical:before {
  content: "\f7e6";
}

.fa-book-open:before {
  content: "\f518";
}

.fa-book-reader:before {
  content: "\f5da";
}

.fa-bookmark:before {
  content: "\f02e";
}

.fa-bootstrap:before {
  content: "\f836";
}

.fa-border-all:before {
  content: "\f84c";
}

.fa-border-none:before {
  content: "\f850";
}

.fa-border-style:before {
  content: "\f853";
}

.fa-bowling-ball:before {
  content: "\f436";
}

.fa-box:before {
  content: "\f466";
}

.fa-box-open:before {
  content: "\f49e";
}

.fa-boxes:before {
  content: "\f468";
}

.fa-braille:before {
  content: "\f2a1";
}

.fa-brain:before {
  content: "\f5dc";
}

.fa-bread-slice:before {
  content: "\f7ec";
}

.fa-briefcase:before {
  content: "\f0b1";
}

.fa-briefcase-medical:before {
  content: "\f469";
}

.fa-broadcast-tower:before {
  content: "\f519";
}

.fa-broom:before {
  content: "\f51a";
}

.fa-brush:before {
  content: "\f55d";
}

.fa-btc:before {
  content: "\f15a";
}

.fa-buffer:before {
  content: "\f837";
}

.fa-bug:before {
  content: "\f188";
}

.fa-building:before {
  content: "\f1ad";
}

.fa-bullhorn:before {
  content: "\f0a1";
}

.fa-bullseye:before {
  content: "\f140";
}

.fa-burn:before {
  content: "\f46a";
}

.fa-buromobelexperte:before {
  content: "\f37f";
}

.fa-bus:before {
  content: "\f207";
}

.fa-bus-alt:before {
  content: "\f55e";
}

.fa-business-time:before {
  content: "\f64a";
}

.fa-buysellads:before {
  content: "\f20d";
}

.fa-calculator:before {
  content: "\f1ec";
}

.fa-calendar:before {
  content: "\f133";
}

.fa-calendar-alt:before {
  content: "\f073";
}

.fa-calendar-check:before {
  content: "\f274";
}

.fa-calendar-day:before {
  content: "\f783";
}

.fa-calendar-minus:before {
  content: "\f272";
}

.fa-calendar-plus:before {
  content: "\f271";
}

.fa-calendar-times:before {
  content: "\f273";
}

.fa-calendar-week:before {
  content: "\f784";
}

.fa-camera:before {
  content: "\f030";
}

.fa-camera-retro:before {
  content: "\f083";
}

.fa-campground:before {
  content: "\f6bb";
}

.fa-canadian-maple-leaf:before {
  content: "\f785";
}

.fa-candy-cane:before {
  content: "\f786";
}

.fa-cannabis:before {
  content: "\f55f";
}

.fa-capsules:before {
  content: "\f46b";
}

.fa-car:before {
  content: "\f1b9";
}

.fa-car-alt:before {
  content: "\f5de";
}

.fa-car-battery:before {
  content: "\f5df";
}

.fa-car-crash:before {
  content: "\f5e1";
}

.fa-car-side:before {
  content: "\f5e4";
}

.fa-caret-down:before {
  content: "\f0d7";
}

.fa-caret-left:before {
  content: "\f0d9";
}

.fa-caret-right:before {
  content: "\f0da";
}

.fa-caret-square-down:before {
  content: "\f150";
}

.fa-caret-square-left:before {
  content: "\f191";
}

.fa-caret-square-right:before {
  content: "\f152";
}

.fa-caret-square-up:before {
  content: "\f151";
}

.fa-caret-up:before {
  content: "\f0d8";
}

.fa-carrot:before {
  content: "\f787";
}

.fa-cart-arrow-down:before {
  content: "\f218";
}

.fa-cart-plus:before {
  content: "\f217";
}

.fa-cash-register:before {
  content: "\f788";
}

.fa-cat:before {
  content: "\f6be";
}

.fa-cc-amazon-pay:before {
  content: "\f42d";
}

.fa-cc-amex:before {
  content: "\f1f3";
}

.fa-cc-apple-pay:before {
  content: "\f416";
}

.fa-cc-diners-club:before {
  content: "\f24c";
}

.fa-cc-discover:before {
  content: "\f1f2";
}

.fa-cc-jcb:before {
  content: "\f24b";
}

.fa-cc-mastercard:before {
  content: "\f1f1";
}

.fa-cc-paypal:before {
  content: "\f1f4";
}

.fa-cc-stripe:before {
  content: "\f1f5";
}

.fa-cc-visa:before {
  content: "\f1f0";
}

.fa-centercode:before {
  content: "\f380";
}

.fa-centos:before {
  content: "\f789";
}

.fa-certificate:before {
  content: "\f0a3";
}

.fa-chair:before {
  content: "\f6c0";
}

.fa-chalkboard:before {
  content: "\f51b";
}

.fa-chalkboard-teacher:before {
  content: "\f51c";
}

.fa-charging-station:before {
  content: "\f5e7";
}

.fa-chart-area:before {
  content: "\f1fe";
}

.fa-chart-bar:before {
  content: "\f080";
}

.fa-chart-line:before {
  content: "\f201";
}

.fa-chart-pie:before {
  content: "\f200";
}

.fa-check:before {
  content: "\f00c";
}

.fa-check-circle:before {
  content: "\f058";
}

.fa-check-double:before {
  content: "\f560";
}

.fa-check-square:before {
  content: "\f14a";
}

.fa-cheese:before {
  content: "\f7ef";
}

.fa-chess:before {
  content: "\f439";
}

.fa-chess-bishop:before {
  content: "\f43a";
}

.fa-chess-board:before {
  content: "\f43c";
}

.fa-chess-king:before {
  content: "\f43f";
}

.fa-chess-knight:before {
  content: "\f441";
}

.fa-chess-pawn:before {
  content: "\f443";
}

.fa-chess-queen:before {
  content: "\f445";
}

.fa-chess-rook:before {
  content: "\f447";
}

.fa-chevron-circle-down:before {
  content: "\f13a";
}

.fa-chevron-circle-left:before {
  content: "\f137";
}

.fa-chevron-circle-right:before {
  content: "\f138";
}

.fa-chevron-circle-up:before {
  content: "\f139";
}

.fa-chevron-down:before {
  content: "\f078";
}

.fa-chevron-left:before {
  content: "\f053";
}

.fa-chevron-right:before {
  content: "\f054";
}

.fa-chevron-up:before {
  content: "\f077";
}

.fa-child:before {
  content: "\f1ae";
}

.fa-chrome:before {
  content: "\f268";
}

.fa-chromecast:before {
  content: "\f838";
}

.fa-church:before {
  content: "\f51d";
}

.fa-circle:before {
  content: "\f111";
}

.fa-circle-notch:before {
  content: "\f1ce";
}

.fa-city:before {
  content: "\f64f";
}

.fa-clinic-medical:before {
  content: "\f7f2";
}

.fa-clipboard:before {
  content: "\f328";
}

.fa-clipboard-check:before {
  content: "\f46c";
}

.fa-clipboard-list:before {
  content: "\f46d";
}

.fa-clock:before {
  content: "\f017";
}

.fa-clone:before {
  content: "\f24d";
}

.fa-closed-captioning:before {
  content: "\f20a";
}

.fa-cloud:before {
  content: "\f0c2";
}

.fa-cloud-download-alt:before {
  content: "\f381";
}

.fa-cloud-meatball:before {
  content: "\f73b";
}

.fa-cloud-moon:before {
  content: "\f6c3";
}

.fa-cloud-moon-rain:before {
  content: "\f73c";
}

.fa-cloud-rain:before {
  content: "\f73d";
}

.fa-cloud-showers-heavy:before {
  content: "\f740";
}

.fa-cloud-sun:before {
  content: "\f6c4";
}

.fa-cloud-sun-rain:before {
  content: "\f743";
}

.fa-cloud-upload-alt:before {
  content: "\f382";
}

.fa-cloudscale:before {
  content: "\f383";
}

.fa-cloudsmith:before {
  content: "\f384";
}

.fa-cloudversify:before {
  content: "\f385";
}

.fa-cocktail:before {
  content: "\f561";
}

.fa-code:before {
  content: "\f121";
}

.fa-code-branch:before {
  content: "\f126";
}

.fa-codepen:before {
  content: "\f1cb";
}

.fa-codiepie:before {
  content: "\f284";
}

.fa-coffee:before {
  content: "\f0f4";
}

.fa-cog:before {
  content: "\f013";
}

.fa-cogs:before {
  content: "\f085";
}

.fa-coins:before {
  content: "\f51e";
}

.fa-columns:before {
  content: "\f0db";
}

.fa-comment:before {
  content: "\f075";
}

.fa-comment-alt:before {
  content: "\f27a";
}

.fa-comment-dollar:before {
  content: "\f651";
}

.fa-comment-dots:before {
  content: "\f4ad";
}

.fa-comment-medical:before {
  content: "\f7f5";
}

.fa-comment-slash:before {
  content: "\f4b3";
}

.fa-comments:before {
  content: "\f086";
}

.fa-comments-dollar:before {
  content: "\f653";
}

.fa-compact-disc:before {
  content: "\f51f";
}

.fa-compass:before {
  content: "\f14e";
}

.fa-compress:before {
  content: "\f066";
}

.fa-compress-arrows-alt:before {
  content: "\f78c";
}

.fa-concierge-bell:before {
  content: "\f562";
}

.fa-confluence:before {
  content: "\f78d";
}

.fa-connectdevelop:before {
  content: "\f20e";
}

.fa-contao:before {
  content: "\f26d";
}

.fa-cookie:before {
  content: "\f563";
}

.fa-cookie-bite:before {
  content: "\f564";
}

.fa-copy:before {
  content: "\f0c5";
}

.fa-copyright:before {
  content: "\f1f9";
}

.fa-cotton-bureau:before {
  content: "\f89e";
}

.fa-couch:before {
  content: "\f4b8";
}

.fa-cpanel:before {
  content: "\f388";
}

.fa-creative-commons:before {
  content: "\f25e";
}

.fa-creative-commons-by:before {
  content: "\f4e7";
}

.fa-creative-commons-nc:before {
  content: "\f4e8";
}

.fa-creative-commons-nc-eu:before {
  content: "\f4e9";
}

.fa-creative-commons-nc-jp:before {
  content: "\f4ea";
}

.fa-creative-commons-nd:before {
  content: "\f4eb";
}

.fa-creative-commons-pd:before {
  content: "\f4ec";
}

.fa-creative-commons-pd-alt:before {
  content: "\f4ed";
}

.fa-creative-commons-remix:before {
  content: "\f4ee";
}

.fa-creative-commons-sa:before {
  content: "\f4ef";
}

.fa-creative-commons-sampling:before {
  content: "\f4f0";
}

.fa-creative-commons-sampling-plus:before {
  content: "\f4f1";
}

.fa-creative-commons-share:before {
  content: "\f4f2";
}

.fa-creative-commons-zero:before {
  content: "\f4f3";
}

.fa-credit-card:before {
  content: "\f09d";
}

.fa-critical-role:before {
  content: "\f6c9";
}

.fa-crop:before {
  content: "\f125";
}

.fa-crop-alt:before {
  content: "\f565";
}

.fa-cross:before {
  content: "\f654";
}

.fa-crosshairs:before {
  content: "\f05b";
}

.fa-crow:before {
  content: "\f520";
}

.fa-crown:before {
  content: "\f521";
}

.fa-crutch:before {
  content: "\f7f7";
}

.fa-css3:before {
  content: "\f13c";
}

.fa-css3-alt:before {
  content: "\f38b";
}

.fa-cube:before {
  content: "\f1b2";
}

.fa-cubes:before {
  content: "\f1b3";
}

.fa-cut:before {
  content: "\f0c4";
}

.fa-cuttlefish:before {
  content: "\f38c";
}

.fa-d-and-d:before {
  content: "\f38d";
}

.fa-d-and-d-beyond:before {
  content: "\f6ca";
}

.fa-dashcube:before {
  content: "\f210";
}

.fa-database:before {
  content: "\f1c0";
}

.fa-deaf:before {
  content: "\f2a4";
}

.fa-delicious:before {
  content: "\f1a5";
}

.fa-democrat:before {
  content: "\f747";
}

.fa-deploydog:before {
  content: "\f38e";
}

.fa-deskpro:before {
  content: "\f38f";
}

.fa-desktop:before {
  content: "\f108";
}

.fa-dev:before {
  content: "\f6cc";
}

.fa-deviantart:before {
  content: "\f1bd";
}

.fa-dharmachakra:before {
  content: "\f655";
}

.fa-dhl:before {
  content: "\f790";
}

.fa-diagnoses:before {
  content: "\f470";
}

.fa-diaspora:before {
  content: "\f791";
}

.fa-dice:before {
  content: "\f522";
}

.fa-dice-d20:before {
  content: "\f6cf";
}

.fa-dice-d6:before {
  content: "\f6d1";
}

.fa-dice-five:before {
  content: "\f523";
}

.fa-dice-four:before {
  content: "\f524";
}

.fa-dice-one:before {
  content: "\f525";
}

.fa-dice-six:before {
  content: "\f526";
}

.fa-dice-three:before {
  content: "\f527";
}

.fa-dice-two:before {
  content: "\f528";
}

.fa-digg:before {
  content: "\f1a6";
}

.fa-digital-ocean:before {
  content: "\f391";
}

.fa-digital-tachograph:before {
  content: "\f566";
}

.fa-directions:before {
  content: "\f5eb";
}

.fa-discord:before {
  content: "\f392";
}

.fa-discourse:before {
  content: "\f393";
}

.fa-divide:before {
  content: "\f529";
}

.fa-dizzy:before {
  content: "\f567";
}

.fa-dna:before {
  content: "\f471";
}

.fa-dochub:before {
  content: "\f394";
}

.fa-docker:before {
  content: "\f395";
}

.fa-dog:before {
  content: "\f6d3";
}

.fa-dollar-sign:before {
  content: "\f155";
}

.fa-dolly:before {
  content: "\f472";
}

.fa-dolly-flatbed:before {
  content: "\f474";
}

.fa-donate:before {
  content: "\f4b9";
}

.fa-door-closed:before {
  content: "\f52a";
}

.fa-door-open:before {
  content: "\f52b";
}

.fa-dot-circle:before {
  content: "\f192";
}

.fa-dove:before {
  content: "\f4ba";
}

.fa-download:before {
  content: "\f019";
}

.fa-draft2digital:before {
  content: "\f396";
}

.fa-drafting-compass:before {
  content: "\f568";
}

.fa-dragon:before {
  content: "\f6d5";
}

.fa-draw-polygon:before {
  content: "\f5ee";
}

.fa-dribbble:before {
  content: "\f17d";
}

.fa-dribbble-square:before {
  content: "\f397";
}

.fa-dropbox:before {
  content: "\f16b";
}

.fa-drum:before {
  content: "\f569";
}

.fa-drum-steelpan:before {
  content: "\f56a";
}

.fa-drumstick-bite:before {
  content: "\f6d7";
}

.fa-drupal:before {
  content: "\f1a9";
}

.fa-dumbbell:before {
  content: "\f44b";
}

.fa-dumpster:before {
  content: "\f793";
}

.fa-dumpster-fire:before {
  content: "\f794";
}

.fa-dungeon:before {
  content: "\f6d9";
}

.fa-dyalog:before {
  content: "\f399";
}

.fa-earlybirds:before {
  content: "\f39a";
}

.fa-ebay:before {
  content: "\f4f4";
}

.fa-edge:before {
  content: "\f282";
}

.fa-edit:before {
  content: "\f044";
}

.fa-egg:before {
  content: "\f7fb";
}

.fa-eject:before {
  content: "\f052";
}

.fa-elementor:before {
  content: "\f430";
}

.fa-ellipsis-h:before {
  content: "\f141";
}

.fa-ellipsis-v:before {
  content: "\f142";
}

.fa-ello:before {
  content: "\f5f1";
}

.fa-ember:before {
  content: "\f423";
}

.fa-empire:before {
  content: "\f1d1";
}

.fa-envelope:before {
  content: "\f0e0";
}

.fa-envelope-open:before {
  content: "\f2b6";
}

.fa-envelope-open-text:before {
  content: "\f658";
}

.fa-envelope-square:before {
  content: "\f199";
}

.fa-envira:before {
  content: "\f299";
}

.fa-equals:before {
  content: "\f52c";
}

.fa-eraser:before {
  content: "\f12d";
}

.fa-erlang:before {
  content: "\f39d";
}

.fa-ethereum:before {
  content: "\f42e";
}

.fa-ethernet:before {
  content: "\f796";
}

.fa-etsy:before {
  content: "\f2d7";
}

.fa-euro-sign:before {
  content: "\f153";
}

.fa-evernote:before {
  content: "\f839";
}

.fa-exchange-alt:before {
  content: "\f362";
}

.fa-exclamation:before {
  content: "\f12a";
}

.fa-exclamation-circle:before {
  content: "\f06a";
}

.fa-exclamation-triangle:before {
  content: "\f071";
}

.fa-expand:before {
  content: "\f065";
}

.fa-expand-arrows-alt:before {
  content: "\f31e";
}

.fa-expeditedssl:before {
  content: "\f23e";
}

.fa-external-link-alt:before {
  content: "\f35d";
}

.fa-external-link-square-alt:before {
  content: "\f360";
}

.fa-eye:before {
  content: "\f06e";
}

.fa-eye-dropper:before {
  content: "\f1fb";
}

.fa-eye-slash:before {
  content: "\f070";
}

.fa-facebook:before {
  content: "\f09a";
}

.fa-facebook-f:before {
  content: "\f39e";
}

.fa-facebook-messenger:before {
  content: "\f39f";
}

.fa-facebook-square:before {
  content: "\f082";
}

.fa-fan:before {
  content: "\f863";
}

.fa-fantasy-flight-games:before {
  content: "\f6dc";
}

.fa-fast-backward:before {
  content: "\f049";
}

.fa-fast-forward:before {
  content: "\f050";
}

.fa-fax:before {
  content: "\f1ac";
}

.fa-feather:before {
  content: "\f52d";
}

.fa-feather-alt:before {
  content: "\f56b";
}

.fa-fedex:before {
  content: "\f797";
}

.fa-fedora:before {
  content: "\f798";
}

.fa-female:before {
  content: "\f182";
}

.fa-fighter-jet:before {
  content: "\f0fb";
}

.fa-figma:before {
  content: "\f799";
}

.fa-file:before {
  content: "\f15b";
}

.fa-file-alt:before {
  content: "\f15c";
}

.fa-file-archive:before {
  content: "\f1c6";
}

.fa-file-audio:before {
  content: "\f1c7";
}

.fa-file-code:before {
  content: "\f1c9";
}

.fa-file-contract:before {
  content: "\f56c";
}

.fa-file-csv:before {
  content: "\f6dd";
}

.fa-file-download:before {
  content: "\f56d";
}

.fa-file-excel:before {
  content: "\f1c3";
}

.fa-file-export:before {
  content: "\f56e";
}

.fa-file-image:before {
  content: "\f1c5";
}

.fa-file-import:before {
  content: "\f56f";
}

.fa-file-invoice:before {
  content: "\f570";
}

.fa-file-invoice-dollar:before {
  content: "\f571";
}

.fa-file-medical:before {
  content: "\f477";
}

.fa-file-medical-alt:before {
  content: "\f478";
}

.fa-file-pdf:before {
  content: "\f1c1";
}

.fa-file-powerpoint:before {
  content: "\f1c4";
}

.fa-file-prescription:before {
  content: "\f572";
}

.fa-file-signature:before {
  content: "\f573";
}

.fa-file-upload:before {
  content: "\f574";
}

.fa-file-video:before {
  content: "\f1c8";
}

.fa-file-word:before {
  content: "\f1c2";
}

.fa-fill:before {
  content: "\f575";
}

.fa-fill-drip:before {
  content: "\f576";
}

.fa-film:before {
  content: "\f008";
}

.fa-filter:before {
  content: "\f0b0";
}

.fa-fingerprint:before {
  content: "\f577";
}

.fa-fire:before {
  content: "\f06d";
}

.fa-fire-alt:before {
  content: "\f7e4";
}

.fa-fire-extinguisher:before {
  content: "\f134";
}

.fa-firefox:before {
  content: "\f269";
}

.fa-first-aid:before {
  content: "\f479";
}

.fa-first-order:before {
  content: "\f2b0";
}

.fa-first-order-alt:before {
  content: "\f50a";
}

.fa-firstdraft:before {
  content: "\f3a1";
}

.fa-fish:before {
  content: "\f578";
}

.fa-fist-raised:before {
  content: "\f6de";
}

.fa-flag:before {
  content: "\f024";
}

.fa-flag-checkered:before {
  content: "\f11e";
}

.fa-flag-usa:before {
  content: "\f74d";
}

.fa-flask:before {
  content: "\f0c3";
}

.fa-flickr:before {
  content: "\f16e";
}

.fa-flipboard:before {
  content: "\f44d";
}

.fa-flushed:before {
  content: "\f579";
}

.fa-fly:before {
  content: "\f417";
}

.fa-folder:before {
  content: "\f07b";
}

.fa-folder-minus:before {
  content: "\f65d";
}

.fa-folder-open:before {
  content: "\f07c";
}

.fa-folder-plus:before {
  content: "\f65e";
}

.fa-font:before {
  content: "\f031";
}

.fa-font-awesome:before {
  content: "\f2b4";
}

.fa-font-awesome-alt:before {
  content: "\f35c";
}

.fa-font-awesome-flag:before {
  content: "\f425";
}

.fa-font-awesome-logo-full:before {
  content: "\f4e6";
}

.fa-fonticons:before {
  content: "\f280";
}

.fa-fonticons-fi:before {
  content: "\f3a2";
}

.fa-football-ball:before {
  content: "\f44e";
}

.fa-fort-awesome:before {
  content: "\f286";
}

.fa-fort-awesome-alt:before {
  content: "\f3a3";
}

.fa-forumbee:before {
  content: "\f211";
}

.fa-forward:before {
  content: "\f04e";
}

.fa-foursquare:before {
  content: "\f180";
}

.fa-free-code-camp:before {
  content: "\f2c5";
}

.fa-freebsd:before {
  content: "\f3a4";
}

.fa-frog:before {
  content: "\f52e";
}

.fa-frown:before {
  content: "\f119";
}

.fa-frown-open:before {
  content: "\f57a";
}

.fa-fulcrum:before {
  content: "\f50b";
}

.fa-funnel-dollar:before {
  content: "\f662";
}

.fa-futbol:before {
  content: "\f1e3";
}

.fa-galactic-republic:before {
  content: "\f50c";
}

.fa-galactic-senate:before {
  content: "\f50d";
}

.fa-gamepad:before {
  content: "\f11b";
}

.fa-gas-pump:before {
  content: "\f52f";
}

.fa-gavel:before {
  content: "\f0e3";
}

.fa-gem:before {
  content: "\f3a5";
}

.fa-genderless:before {
  content: "\f22d";
}

.fa-get-pocket:before {
  content: "\f265";
}

.fa-gg:before {
  content: "\f260";
}

.fa-gg-circle:before {
  content: "\f261";
}

.fa-ghost:before {
  content: "\f6e2";
}

.fa-gift:before {
  content: "\f06b";
}

.fa-gifts:before {
  content: "\f79c";
}

.fa-git:before {
  content: "\f1d3";
}

.fa-git-alt:before {
  content: "\f841";
}

.fa-git-square:before {
  content: "\f1d2";
}

.fa-github:before {
  content: "\f09b";
}

.fa-github-alt:before {
  content: "\f113";
}

.fa-github-square:before {
  content: "\f092";
}

.fa-gitkraken:before {
  content: "\f3a6";
}

.fa-gitlab:before {
  content: "\f296";
}

.fa-gitter:before {
  content: "\f426";
}

.fa-glass-cheers:before {
  content: "\f79f";
}

.fa-glass-martini:before {
  content: "\f000";
}

.fa-glass-martini-alt:before {
  content: "\f57b";
}

.fa-glass-whiskey:before {
  content: "\f7a0";
}

.fa-glasses:before {
  content: "\f530";
}

.fa-glide:before {
  content: "\f2a5";
}

.fa-glide-g:before {
  content: "\f2a6";
}

.fa-globe:before {
  content: "\f0ac";
}

.fa-globe-africa:before {
  content: "\f57c";
}

.fa-globe-americas:before {
  content: "\f57d";
}

.fa-globe-asia:before {
  content: "\f57e";
}

.fa-globe-europe:before {
  content: "\f7a2";
}

.fa-gofore:before {
  content: "\f3a7";
}

.fa-golf-ball:before {
  content: "\f450";
}

.fa-goodreads:before {
  content: "\f3a8";
}

.fa-goodreads-g:before {
  content: "\f3a9";
}

.fa-google:before {
  content: "\f1a0";
}

.fa-google-drive:before {
  content: "\f3aa";
}

.fa-google-play:before {
  content: "\f3ab";
}

.fa-google-plus:before {
  content: "\f2b3";
}

.fa-google-plus-g:before {
  content: "\f0d5";
}

.fa-google-plus-square:before {
  content: "\f0d4";
}

.fa-google-wallet:before {
  content: "\f1ee";
}

.fa-gopuram:before {
  content: "\f664";
}

.fa-graduation-cap:before {
  content: "\f19d";
}

.fa-gratipay:before {
  content: "\f184";
}

.fa-grav:before {
  content: "\f2d6";
}

.fa-greater-than:before {
  content: "\f531";
}

.fa-greater-than-equal:before {
  content: "\f532";
}

.fa-grimace:before {
  content: "\f57f";
}

.fa-grin:before {
  content: "\f580";
}

.fa-grin-alt:before {
  content: "\f581";
}

.fa-grin-beam:before {
  content: "\f582";
}

.fa-grin-beam-sweat:before {
  content: "\f583";
}

.fa-grin-hearts:before {
  content: "\f584";
}

.fa-grin-squint:before {
  content: "\f585";
}

.fa-grin-squint-tears:before {
  content: "\f586";
}

.fa-grin-stars:before {
  content: "\f587";
}

.fa-grin-tears:before {
  content: "\f588";
}

.fa-grin-tongue:before {
  content: "\f589";
}

.fa-grin-tongue-squint:before {
  content: "\f58a";
}

.fa-grin-tongue-wink:before {
  content: "\f58b";
}

.fa-grin-wink:before {
  content: "\f58c";
}

.fa-grip-horizontal:before {
  content: "\f58d";
}

.fa-grip-lines:before {
  content: "\f7a4";
}

.fa-grip-lines-vertical:before {
  content: "\f7a5";
}

.fa-grip-vertical:before {
  content: "\f58e";
}

.fa-gripfire:before {
  content: "\f3ac";
}

.fa-grunt:before {
  content: "\f3ad";
}

.fa-guitar:before {
  content: "\f7a6";
}

.fa-gulp:before {
  content: "\f3ae";
}

.fa-h-square:before {
  content: "\f0fd";
}

.fa-hacker-news:before {
  content: "\f1d4";
}

.fa-hacker-news-square:before {
  content: "\f3af";
}

.fa-hackerrank:before {
  content: "\f5f7";
}

.fa-hamburger:before {
  content: "\f805";
}

.fa-hammer:before {
  content: "\f6e3";
}

.fa-hamsa:before {
  content: "\f665";
}

.fa-hand-holding:before {
  content: "\f4bd";
}

.fa-hand-holding-heart:before {
  content: "\f4be";
}

.fa-hand-holding-usd:before {
  content: "\f4c0";
}

.fa-hand-lizard:before {
  content: "\f258";
}

.fa-hand-middle-finger:before {
  content: "\f806";
}

.fa-hand-paper:before {
  content: "\f256";
}

.fa-hand-peace:before {
  content: "\f25b";
}

.fa-hand-point-down:before {
  content: "\f0a7";
}

.fa-hand-point-left:before {
  content: "\f0a5";
}

.fa-hand-point-right:before {
  content: "\f0a4";
}

.fa-hand-point-up:before {
  content: "\f0a6";
}

.fa-hand-pointer:before {
  content: "\f25a";
}

.fa-hand-rock:before {
  content: "\f255";
}

.fa-hand-scissors:before {
  content: "\f257";
}

.fa-hand-spock:before {
  content: "\f259";
}

.fa-hands:before {
  content: "\f4c2";
}

.fa-hands-helping:before {
  content: "\f4c4";
}

.fa-handshake:before {
  content: "\f2b5";
}

.fa-hanukiah:before {
  content: "\f6e6";
}

.fa-hard-hat:before {
  content: "\f807";
}

.fa-hashtag:before {
  content: "\f292";
}

.fa-hat-wizard:before {
  content: "\f6e8";
}

.fa-haykal:before {
  content: "\f666";
}

.fa-hdd:before {
  content: "\f0a0";
}

.fa-heading:before {
  content: "\f1dc";
}

.fa-headphones:before {
  content: "\f025";
}

.fa-headphones-alt:before {
  content: "\f58f";
}

.fa-headset:before {
  content: "\f590";
}

.fa-heart:before {
  content: "\f004";
}

.fa-heart-broken:before {
  content: "\f7a9";
}

.fa-heartbeat:before {
  content: "\f21e";
}

.fa-helicopter:before {
  content: "\f533";
}

.fa-highlighter:before {
  content: "\f591";
}

.fa-hiking:before {
  content: "\f6ec";
}

.fa-hippo:before {
  content: "\f6ed";
}

.fa-hips:before {
  content: "\f452";
}

.fa-hire-a-helper:before {
  content: "\f3b0";
}

.fa-history:before {
  content: "\f1da";
}

.fa-hockey-puck:before {
  content: "\f453";
}

.fa-holly-berry:before {
  content: "\f7aa";
}

.fa-home:before {
  content: "\f015";
}

.fa-hooli:before {
  content: "\f427";
}

.fa-hornbill:before {
  content: "\f592";
}

.fa-horse:before {
  content: "\f6f0";
}

.fa-horse-head:before {
  content: "\f7ab";
}

.fa-hospital:before {
  content: "\f0f8";
}

.fa-hospital-alt:before {
  content: "\f47d";
}

.fa-hospital-symbol:before {
  content: "\f47e";
}

.fa-hot-tub:before {
  content: "\f593";
}

.fa-hotdog:before {
  content: "\f80f";
}

.fa-hotel:before {
  content: "\f594";
}

.fa-hotjar:before {
  content: "\f3b1";
}

.fa-hourglass:before {
  content: "\f254";
}

.fa-hourglass-end:before {
  content: "\f253";
}

.fa-hourglass-half:before {
  content: "\f252";
}

.fa-hourglass-start:before {
  content: "\f251";
}

.fa-house-damage:before {
  content: "\f6f1";
}

.fa-houzz:before {
  content: "\f27c";
}

.fa-hryvnia:before {
  content: "\f6f2";
}

.fa-html5:before {
  content: "\f13b";
}

.fa-hubspot:before {
  content: "\f3b2";
}

.fa-i-cursor:before {
  content: "\f246";
}

.fa-ice-cream:before {
  content: "\f810";
}

.fa-icicles:before {
  content: "\f7ad";
}

.fa-icons:before {
  content: "\f86d";
}

.fa-id-badge:before {
  content: "\f2c1";
}

.fa-id-card:before {
  content: "\f2c2";
}

.fa-id-card-alt:before {
  content: "\f47f";
}

.fa-igloo:before {
  content: "\f7ae";
}

.fa-image:before {
  content: "\f03e";
}

.fa-images:before {
  content: "\f302";
}

.fa-imdb:before {
  content: "\f2d8";
}

.fa-inbox:before {
  content: "\f01c";
}

.fa-indent:before {
  content: "\f03c";
}

.fa-industry:before {
  content: "\f275";
}

.fa-infinity:before {
  content: "\f534";
}

.fa-info:before {
  content: "\f129";
}

.fa-info-circle:before {
  content: "\f05a";
}

.fa-instagram:before {
  content: "\f16d";
}

.fa-intercom:before {
  content: "\f7af";
}

.fa-internet-explorer:before {
  content: "\f26b";
}

.fa-invision:before {
  content: "\f7b0";
}

.fa-ioxhost:before {
  content: "\f208";
}

.fa-italic:before {
  content: "\f033";
}

.fa-itch-io:before {
  content: "\f83a";
}

.fa-itunes:before {
  content: "\f3b4";
}

.fa-itunes-note:before {
  content: "\f3b5";
}

.fa-java:before {
  content: "\f4e4";
}

.fa-jedi:before {
  content: "\f669";
}

.fa-jedi-order:before {
  content: "\f50e";
}

.fa-jenkins:before {
  content: "\f3b6";
}

.fa-jira:before {
  content: "\f7b1";
}

.fa-joget:before {
  content: "\f3b7";
}

.fa-joint:before {
  content: "\f595";
}

.fa-joomla:before {
  content: "\f1aa";
}

.fa-journal-whills:before {
  content: "\f66a";
}

.fa-js:before {
  content: "\f3b8";
}

.fa-js-square:before {
  content: "\f3b9";
}

.fa-jsfiddle:before {
  content: "\f1cc";
}

.fa-kaaba:before {
  content: "\f66b";
}

.fa-kaggle:before {
  content: "\f5fa";
}

.fa-key:before {
  content: "\f084";
}

.fa-keybase:before {
  content: "\f4f5";
}

.fa-keyboard:before {
  content: "\f11c";
}

.fa-keycdn:before {
  content: "\f3ba";
}

.fa-khanda:before {
  content: "\f66d";
}

.fa-kickstarter:before {
  content: "\f3bb";
}

.fa-kickstarter-k:before {
  content: "\f3bc";
}

.fa-kiss:before {
  content: "\f596";
}

.fa-kiss-beam:before {
  content: "\f597";
}

.fa-kiss-wink-heart:before {
  content: "\f598";
}

.fa-kiwi-bird:before {
  content: "\f535";
}

.fa-korvue:before {
  content: "\f42f";
}

.fa-landmark:before {
  content: "\f66f";
}

.fa-language:before {
  content: "\f1ab";
}

.fa-laptop:before {
  content: "\f109";
}

.fa-laptop-code:before {
  content: "\f5fc";
}

.fa-laptop-medical:before {
  content: "\f812";
}

.fa-laravel:before {
  content: "\f3bd";
}

.fa-lastfm:before {
  content: "\f202";
}

.fa-lastfm-square:before {
  content: "\f203";
}

.fa-laugh:before {
  content: "\f599";
}

.fa-laugh-beam:before {
  content: "\f59a";
}

.fa-laugh-squint:before {
  content: "\f59b";
}

.fa-laugh-wink:before {
  content: "\f59c";
}

.fa-layer-group:before {
  content: "\f5fd";
}

.fa-leaf:before {
  content: "\f06c";
}

.fa-leanpub:before {
  content: "\f212";
}

.fa-lemon:before {
  content: "\f094";
}

.fa-less:before {
  content: "\f41d";
}

.fa-less-than:before {
  content: "\f536";
}

.fa-less-than-equal:before {
  content: "\f537";
}

.fa-level-down-alt:before {
  content: "\f3be";
}

.fa-level-up-alt:before {
  content: "\f3bf";
}

.fa-life-ring:before {
  content: "\f1cd";
}

.fa-lightbulb:before {
  content: "\f0eb";
}

.fa-line:before {
  content: "\f3c0";
}

.fa-link:before {
  content: "\f0c1";
}

.fa-linkedin:before {
  content: "\f08c";
}

.fa-linkedin-in:before {
  content: "\f0e1";
}

.fa-linode:before {
  content: "\f2b8";
}

.fa-linux:before {
  content: "\f17c";
}

.fa-lira-sign:before {
  content: "\f195";
}

.fa-list:before {
  content: "\f03a";
}

.fa-list-alt:before {
  content: "\f022";
}

.fa-list-ol:before {
  content: "\f0cb";
}

.fa-list-ul:before {
  content: "\f0ca";
}

.fa-location-arrow:before {
  content: "\f124";
}

.fa-lock:before {
  content: "\f023";
}

.fa-lock-open:before {
  content: "\f3c1";
}

.fa-long-arrow-alt-down:before {
  content: "\f309";
}

.fa-long-arrow-alt-left:before {
  content: "\f30a";
}

.fa-long-arrow-alt-right:before {
  content: "\f30b";
}

.fa-long-arrow-alt-up:before {
  content: "\f30c";
}

.fa-low-vision:before {
  content: "\f2a8";
}

.fa-luggage-cart:before {
  content: "\f59d";
}

.fa-lyft:before {
  content: "\f3c3";
}

.fa-magento:before {
  content: "\f3c4";
}

.fa-magic:before {
  content: "\f0d0";
}

.fa-magnet:before {
  content: "\f076";
}

.fa-mail-bulk:before {
  content: "\f674";
}

.fa-mailchimp:before {
  content: "\f59e";
}

.fa-male:before {
  content: "\f183";
}

.fa-mandalorian:before {
  content: "\f50f";
}

.fa-map:before {
  content: "\f279";
}

.fa-map-marked:before {
  content: "\f59f";
}

.fa-map-marked-alt:before {
  content: "\f5a0";
}

.fa-map-marker:before {
  content: "\f041";
}

.fa-map-marker-alt:before {
  content: "\f3c5";
}

.fa-map-pin:before {
  content: "\f276";
}

.fa-map-signs:before {
  content: "\f277";
}

.fa-markdown:before {
  content: "\f60f";
}

.fa-marker:before {
  content: "\f5a1";
}

.fa-mars:before {
  content: "\f222";
}

.fa-mars-double:before {
  content: "\f227";
}

.fa-mars-stroke:before {
  content: "\f229";
}

.fa-mars-stroke-h:before {
  content: "\f22b";
}

.fa-mars-stroke-v:before {
  content: "\f22a";
}

.fa-mask:before {
  content: "\f6fa";
}

.fa-mastodon:before {
  content: "\f4f6";
}

.fa-maxcdn:before {
  content: "\f136";
}

.fa-medal:before {
  content: "\f5a2";
}

.fa-medapps:before {
  content: "\f3c6";
}

.fa-medium:before {
  content: "\f23a";
}

.fa-medium-m:before {
  content: "\f3c7";
}

.fa-medkit:before {
  content: "\f0fa";
}

.fa-medrt:before {
  content: "\f3c8";
}

.fa-meetup:before {
  content: "\f2e0";
}

.fa-megaport:before {
  content: "\f5a3";
}

.fa-meh:before {
  content: "\f11a";
}

.fa-meh-blank:before {
  content: "\f5a4";
}

.fa-meh-rolling-eyes:before {
  content: "\f5a5";
}

.fa-memory:before {
  content: "\f538";
}

.fa-mendeley:before {
  content: "\f7b3";
}

.fa-menorah:before {
  content: "\f676";
}

.fa-mercury:before {
  content: "\f223";
}

.fa-meteor:before {
  content: "\f753";
}

.fa-microchip:before {
  content: "\f2db";
}

.fa-microphone:before {
  content: "\f130";
}

.fa-microphone-alt:before {
  content: "\f3c9";
}

.fa-microphone-alt-slash:before {
  content: "\f539";
}

.fa-microphone-slash:before {
  content: "\f131";
}

.fa-microscope:before {
  content: "\f610";
}

.fa-microsoft:before {
  content: "\f3ca";
}

.fa-minus:before {
  content: "\f068";
}

.fa-minus-circle:before {
  content: "\f056";
}

.fa-minus-square:before {
  content: "\f146";
}

.fa-mitten:before {
  content: "\f7b5";
}

.fa-mix:before {
  content: "\f3cb";
}

.fa-mixcloud:before {
  content: "\f289";
}

.fa-mizuni:before {
  content: "\f3cc";
}

.fa-mobile:before {
  content: "\f10b";
}

.fa-mobile-alt:before {
  content: "\f3cd";
}

.fa-modx:before {
  content: "\f285";
}

.fa-monero:before {
  content: "\f3d0";
}

.fa-money-bill:before {
  content: "\f0d6";
}

.fa-money-bill-alt:before {
  content: "\f3d1";
}

.fa-money-bill-wave:before {
  content: "\f53a";
}

.fa-money-bill-wave-alt:before {
  content: "\f53b";
}

.fa-money-check:before {
  content: "\f53c";
}

.fa-money-check-alt:before {
  content: "\f53d";
}

.fa-monument:before {
  content: "\f5a6";
}

.fa-moon:before {
  content: "\f186";
}

.fa-mortar-pestle:before {
  content: "\f5a7";
}

.fa-mosque:before {
  content: "\f678";
}

.fa-motorcycle:before {
  content: "\f21c";
}

.fa-mountain:before {
  content: "\f6fc";
}

.fa-mouse-pointer:before {
  content: "\f245";
}

.fa-mug-hot:before {
  content: "\f7b6";
}

.fa-music:before {
  content: "\f001";
}

.fa-napster:before {
  content: "\f3d2";
}

.fa-neos:before {
  content: "\f612";
}

.fa-network-wired:before {
  content: "\f6ff";
}

.fa-neuter:before {
  content: "\f22c";
}

.fa-newspaper:before {
  content: "\f1ea";
}

.fa-nimblr:before {
  content: "\f5a8";
}

.fa-node:before {
  content: "\f419";
}

.fa-node-js:before {
  content: "\f3d3";
}

.fa-not-equal:before {
  content: "\f53e";
}

.fa-notes-medical:before {
  content: "\f481";
}

.fa-npm:before {
  content: "\f3d4";
}

.fa-ns8:before {
  content: "\f3d5";
}

.fa-nutritionix:before {
  content: "\f3d6";
}

.fa-object-group:before {
  content: "\f247";
}

.fa-object-ungroup:before {
  content: "\f248";
}

.fa-odnoklassniki:before {
  content: "\f263";
}

.fa-odnoklassniki-square:before {
  content: "\f264";
}

.fa-oil-can:before {
  content: "\f613";
}

.fa-old-republic:before {
  content: "\f510";
}

.fa-om:before {
  content: "\f679";
}

.fa-opencart:before {
  content: "\f23d";
}

.fa-openid:before {
  content: "\f19b";
}

.fa-opera:before {
  content: "\f26a";
}

.fa-optin-monster:before {
  content: "\f23c";
}

.fa-osi:before {
  content: "\f41a";
}

.fa-otter:before {
  content: "\f700";
}

.fa-outdent:before {
  content: "\f03b";
}

.fa-page4:before {
  content: "\f3d7";
}

.fa-pagelines:before {
  content: "\f18c";
}

.fa-pager:before {
  content: "\f815";
}

.fa-paint-brush:before {
  content: "\f1fc";
}

.fa-paint-roller:before {
  content: "\f5aa";
}

.fa-palette:before {
  content: "\f53f";
}

.fa-palfed:before {
  content: "\f3d8";
}

.fa-pallet:before {
  content: "\f482";
}

.fa-paper-plane:before {
  content: "\f1d8";
}

.fa-paperclip:before {
  content: "\f0c6";
}

.fa-parachute-box:before {
  content: "\f4cd";
}

.fa-paragraph:before {
  content: "\f1dd";
}

.fa-parking:before {
  content: "\f540";
}

.fa-passport:before {
  content: "\f5ab";
}

.fa-pastafarianism:before {
  content: "\f67b";
}

.fa-paste:before {
  content: "\f0ea";
}

.fa-patreon:before {
  content: "\f3d9";
}

.fa-pause:before {
  content: "\f04c";
}

.fa-pause-circle:before {
  content: "\f28b";
}

.fa-paw:before {
  content: "\f1b0";
}

.fa-paypal:before {
  content: "\f1ed";
}

.fa-peace:before {
  content: "\f67c";
}

.fa-pen:before {
  content: "\f304";
}

.fa-pen-alt:before {
  content: "\f305";
}

.fa-pen-fancy:before {
  content: "\f5ac";
}

.fa-pen-nib:before {
  content: "\f5ad";
}

.fa-pen-square:before {
  content: "\f14b";
}

.fa-pencil-alt:before {
  content: "\f303";
}

.fa-pencil-ruler:before {
  content: "\f5ae";
}

.fa-penny-arcade:before {
  content: "\f704";
}

.fa-people-carry:before {
  content: "\f4ce";
}

.fa-pepper-hot:before {
  content: "\f816";
}

.fa-percent:before {
  content: "\f295";
}

.fa-percentage:before {
  content: "\f541";
}

.fa-periscope:before {
  content: "\f3da";
}

.fa-person-booth:before {
  content: "\f756";
}

.fa-phabricator:before {
  content: "\f3db";
}

.fa-phoenix-framework:before {
  content: "\f3dc";
}

.fa-phoenix-squadron:before {
  content: "\f511";
}

.fa-phone:before {
  content: "\f095";
}

.fa-phone-alt:before {
  content: "\f879";
}

.fa-phone-slash:before {
  content: "\f3dd";
}

.fa-phone-square:before {
  content: "\f098";
}

.fa-phone-square-alt:before {
  content: "\f87b";
}

.fa-phone-volume:before {
  content: "\f2a0";
}

.fa-photo-video:before {
  content: "\f87c";
}

.fa-php:before {
  content: "\f457";
}

.fa-pied-piper:before {
  content: "\f2ae";
}

.fa-pied-piper-alt:before {
  content: "\f1a8";
}

.fa-pied-piper-hat:before {
  content: "\f4e5";
}

.fa-pied-piper-pp:before {
  content: "\f1a7";
}

.fa-piggy-bank:before {
  content: "\f4d3";
}

.fa-pills:before {
  content: "\f484";
}

.fa-pinterest:before {
  content: "\f0d2";
}

.fa-pinterest-p:before {
  content: "\f231";
}

.fa-pinterest-square:before {
  content: "\f0d3";
}

.fa-pizza-slice:before {
  content: "\f818";
}

.fa-place-of-worship:before {
  content: "\f67f";
}

.fa-plane:before {
  content: "\f072";
}

.fa-plane-arrival:before {
  content: "\f5af";
}

.fa-plane-departure:before {
  content: "\f5b0";
}

.fa-play:before {
  content: "\f04b";
}

.fa-play-circle:before {
  content: "\f144";
}

.fa-playstation:before {
  content: "\f3df";
}

.fa-plug:before {
  content: "\f1e6";
}

.fa-plus:before {
  content: "\f067";
}

.fa-plus-circle:before {
  content: "\f055";
}

.fa-plus-square:before {
  content: "\f0fe";
}

.fa-podcast:before {
  content: "\f2ce";
}

.fa-poll:before {
  content: "\f681";
}

.fa-poll-h:before {
  content: "\f682";
}

.fa-poo:before {
  content: "\f2fe";
}

.fa-poo-storm:before {
  content: "\f75a";
}

.fa-poop:before {
  content: "\f619";
}

.fa-portrait:before {
  content: "\f3e0";
}

.fa-pound-sign:before {
  content: "\f154";
}

.fa-power-off:before {
  content: "\f011";
}

.fa-pray:before {
  content: "\f683";
}

.fa-praying-hands:before {
  content: "\f684";
}

.fa-prescription:before {
  content: "\f5b1";
}

.fa-prescription-bottle:before {
  content: "\f485";
}

.fa-prescription-bottle-alt:before {
  content: "\f486";
}

.fa-print:before {
  content: "\f02f";
}

.fa-procedures:before {
  content: "\f487";
}

.fa-product-hunt:before {
  content: "\f288";
}

.fa-project-diagram:before {
  content: "\f542";
}

.fa-pushed:before {
  content: "\f3e1";
}

.fa-puzzle-piece:before {
  content: "\f12e";
}

.fa-python:before {
  content: "\f3e2";
}

.fa-qq:before {
  content: "\f1d6";
}

.fa-qrcode:before {
  content: "\f029";
}

.fa-question:before {
  content: "\f128";
}

.fa-question-circle:before {
  content: "\f059";
}

.fa-quidditch:before {
  content: "\f458";
}

.fa-quinscape:before {
  content: "\f459";
}

.fa-quora:before {
  content: "\f2c4";
}

.fa-quote-left:before {
  content: "\f10d";
}

.fa-quote-right:before {
  content: "\f10e";
}

.fa-quran:before {
  content: "\f687";
}

.fa-r-project:before {
  content: "\f4f7";
}

.fa-radiation:before {
  content: "\f7b9";
}

.fa-radiation-alt:before {
  content: "\f7ba";
}

.fa-rainbow:before {
  content: "\f75b";
}

.fa-random:before {
  content: "\f074";
}

.fa-raspberry-pi:before {
  content: "\f7bb";
}

.fa-ravelry:before {
  content: "\f2d9";
}

.fa-react:before {
  content: "\f41b";
}

.fa-reacteurope:before {
  content: "\f75d";
}

.fa-readme:before {
  content: "\f4d5";
}

.fa-rebel:before {
  content: "\f1d0";
}

.fa-receipt:before {
  content: "\f543";
}

.fa-recycle:before {
  content: "\f1b8";
}

.fa-red-river:before {
  content: "\f3e3";
}

.fa-reddit:before {
  content: "\f1a1";
}

.fa-reddit-alien:before {
  content: "\f281";
}

.fa-reddit-square:before {
  content: "\f1a2";
}

.fa-redhat:before {
  content: "\f7bc";
}

.fa-redo:before {
  content: "\f01e";
}

.fa-redo-alt:before {
  content: "\f2f9";
}

.fa-registered:before {
  content: "\f25d";
}

.fa-remove-format:before {
  content: "\f87d";
}

.fa-renren:before {
  content: "\f18b";
}

.fa-reply:before {
  content: "\f3e5";
}

.fa-reply-all:before {
  content: "\f122";
}

.fa-replyd:before {
  content: "\f3e6";
}

.fa-republican:before {
  content: "\f75e";
}

.fa-researchgate:before {
  content: "\f4f8";
}

.fa-resolving:before {
  content: "\f3e7";
}

.fa-restroom:before {
  content: "\f7bd";
}

.fa-retweet:before {
  content: "\f079";
}

.fa-rev:before {
  content: "\f5b2";
}

.fa-ribbon:before {
  content: "\f4d6";
}

.fa-ring:before {
  content: "\f70b";
}

.fa-road:before {
  content: "\f018";
}

.fa-robot:before {
  content: "\f544";
}

.fa-rocket:before {
  content: "\f135";
}

.fa-rocketchat:before {
  content: "\f3e8";
}

.fa-rockrms:before {
  content: "\f3e9";
}

.fa-route:before {
  content: "\f4d7";
}

.fa-rss:before {
  content: "\f09e";
}

.fa-rss-square:before {
  content: "\f143";
}

.fa-ruble-sign:before {
  content: "\f158";
}

.fa-ruler:before {
  content: "\f545";
}

.fa-ruler-combined:before {
  content: "\f546";
}

.fa-ruler-horizontal:before {
  content: "\f547";
}

.fa-ruler-vertical:before {
  content: "\f548";
}

.fa-running:before {
  content: "\f70c";
}

.fa-rupee-sign:before {
  content: "\f156";
}

.fa-sad-cry:before {
  content: "\f5b3";
}

.fa-sad-tear:before {
  content: "\f5b4";
}

.fa-safari:before {
  content: "\f267";
}

.fa-salesforce:before {
  content: "\f83b";
}

.fa-sass:before {
  content: "\f41e";
}

.fa-satellite:before {
  content: "\f7bf";
}

.fa-satellite-dish:before {
  content: "\f7c0";
}

.fa-save:before {
  content: "\f0c7";
}

.fa-schlix:before {
  content: "\f3ea";
}

.fa-school:before {
  content: "\f549";
}

.fa-screwdriver:before {
  content: "\f54a";
}

.fa-scribd:before {
  content: "\f28a";
}

.fa-scroll:before {
  content: "\f70e";
}

.fa-sd-card:before {
  content: "\f7c2";
}

.fa-search:before {
  content: "\f002";
}

.fa-search-dollar:before {
  content: "\f688";
}

.fa-search-location:before {
  content: "\f689";
}

.fa-search-minus:before {
  content: "\f010";
}

.fa-search-plus:before {
  content: "\f00e";
}

.fa-searchengin:before {
  content: "\f3eb";
}

.fa-seedling:before {
  content: "\f4d8";
}

.fa-sellcast:before {
  content: "\f2da";
}

.fa-sellsy:before {
  content: "\f213";
}

.fa-server:before {
  content: "\f233";
}

.fa-servicestack:before {
  content: "\f3ec";
}

.fa-shapes:before {
  content: "\f61f";
}

.fa-share:before {
  content: "\f064";
}

.fa-share-alt:before {
  content: "\f1e0";
}

.fa-share-alt-square:before {
  content: "\f1e1";
}

.fa-share-square:before {
  content: "\f14d";
}

.fa-shekel-sign:before {
  content: "\f20b";
}

.fa-shield-alt:before {
  content: "\f3ed";
}

.fa-ship:before {
  content: "\f21a";
}

.fa-shipping-fast:before {
  content: "\f48b";
}

.fa-shirtsinbulk:before {
  content: "\f214";
}

.fa-shoe-prints:before {
  content: "\f54b";
}

.fa-shopping-bag:before {
  content: "\f290";
}

.fa-shopping-basket:before {
  content: "\f291";
}

.fa-shopping-cart:before {
  content: "\f07a";
}

.fa-shopware:before {
  content: "\f5b5";
}

.fa-shower:before {
  content: "\f2cc";
}

.fa-shuttle-van:before {
  content: "\f5b6";
}

.fa-sign:before {
  content: "\f4d9";
}

.fa-sign-in-alt:before {
  content: "\f2f6";
}

.fa-sign-language:before {
  content: "\f2a7";
}

.fa-sign-out-alt:before {
  content: "\f2f5";
}

.fa-signal:before {
  content: "\f012";
}

.fa-signature:before {
  content: "\f5b7";
}

.fa-sim-card:before {
  content: "\f7c4";
}

.fa-simplybuilt:before {
  content: "\f215";
}

.fa-sistrix:before {
  content: "\f3ee";
}

.fa-sitemap:before {
  content: "\f0e8";
}

.fa-sith:before {
  content: "\f512";
}

.fa-skating:before {
  content: "\f7c5";
}

.fa-sketch:before {
  content: "\f7c6";
}

.fa-skiing:before {
  content: "\f7c9";
}

.fa-skiing-nordic:before {
  content: "\f7ca";
}

.fa-skull:before {
  content: "\f54c";
}

.fa-skull-crossbones:before {
  content: "\f714";
}

.fa-skyatlas:before {
  content: "\f216";
}

.fa-skype:before {
  content: "\f17e";
}

.fa-slack:before {
  content: "\f198";
}

.fa-slack-hash:before {
  content: "\f3ef";
}

.fa-slash:before {
  content: "\f715";
}

.fa-sleigh:before {
  content: "\f7cc";
}

.fa-sliders-h:before {
  content: "\f1de";
}

.fa-slideshare:before {
  content: "\f1e7";
}

.fa-smile:before {
  content: "\f118";
}

.fa-smile-beam:before {
  content: "\f5b8";
}

.fa-smile-wink:before {
  content: "\f4da";
}

.fa-smog:before {
  content: "\f75f";
}

.fa-smoking:before {
  content: "\f48d";
}

.fa-smoking-ban:before {
  content: "\f54d";
}

.fa-sms:before {
  content: "\f7cd";
}

.fa-snapchat:before {
  content: "\f2ab";
}

.fa-snapchat-ghost:before {
  content: "\f2ac";
}

.fa-snapchat-square:before {
  content: "\f2ad";
}

.fa-snowboarding:before {
  content: "\f7ce";
}

.fa-snowflake:before {
  content: "\f2dc";
}

.fa-snowman:before {
  content: "\f7d0";
}

.fa-snowplow:before {
  content: "\f7d2";
}

.fa-socks:before {
  content: "\f696";
}

.fa-solar-panel:before {
  content: "\f5ba";
}

.fa-sort:before {
  content: "\f0dc";
}

.fa-sort-alpha-down:before {
  content: "\f15d";
}

.fa-sort-alpha-down-alt:before {
  content: "\f881";
}

.fa-sort-alpha-up:before {
  content: "\f15e";
}

.fa-sort-alpha-up-alt:before {
  content: "\f882";
}

.fa-sort-amount-down:before {
  content: "\f160";
}

.fa-sort-amount-down-alt:before {
  content: "\f884";
}

.fa-sort-amount-up:before {
  content: "\f161";
}

.fa-sort-amount-up-alt:before {
  content: "\f885";
}

.fa-sort-down:before {
  content: "\f0dd";
}

.fa-sort-numeric-down:before {
  content: "\f162";
}

.fa-sort-numeric-down-alt:before {
  content: "\f886";
}

.fa-sort-numeric-up:before {
  content: "\f163";
}

.fa-sort-numeric-up-alt:before {
  content: "\f887";
}

.fa-sort-up:before {
  content: "\f0de";
}

.fa-soundcloud:before {
  content: "\f1be";
}

.fa-sourcetree:before {
  content: "\f7d3";
}

.fa-spa:before {
  content: "\f5bb";
}

.fa-space-shuttle:before {
  content: "\f197";
}

.fa-speakap:before {
  content: "\f3f3";
}

.fa-speaker-deck:before {
  content: "\f83c";
}

.fa-spell-check:before {
  content: "\f891";
}

.fa-spider:before {
  content: "\f717";
}

.fa-spinner:before {
  content: "\f110";
}

.fa-splotch:before {
  content: "\f5bc";
}

.fa-spotify:before {
  content: "\f1bc";
}

.fa-spray-can:before {
  content: "\f5bd";
}

.fa-square:before {
  content: "\f0c8";
}

.fa-square-full:before {
  content: "\f45c";
}

.fa-square-root-alt:before {
  content: "\f698";
}

.fa-squarespace:before {
  content: "\f5be";
}

.fa-stack-exchange:before {
  content: "\f18d";
}

.fa-stack-overflow:before {
  content: "\f16c";
}

.fa-stackpath:before {
  content: "\f842";
}

.fa-stamp:before {
  content: "\f5bf";
}

.fa-star:before {
  content: "\f005";
}

.fa-star-and-crescent:before {
  content: "\f699";
}

.fa-star-half:before {
  content: "\f089";
}

.fa-star-half-alt:before {
  content: "\f5c0";
}

.fa-star-of-david:before {
  content: "\f69a";
}

.fa-star-of-life:before {
  content: "\f621";
}

.fa-staylinked:before {
  content: "\f3f5";
}

.fa-steam:before {
  content: "\f1b6";
}

.fa-steam-square:before {
  content: "\f1b7";
}

.fa-steam-symbol:before {
  content: "\f3f6";
}

.fa-step-backward:before {
  content: "\f048";
}

.fa-step-forward:before {
  content: "\f051";
}

.fa-stethoscope:before {
  content: "\f0f1";
}

.fa-sticker-mule:before {
  content: "\f3f7";
}

.fa-sticky-note:before {
  content: "\f249";
}

.fa-stop:before {
  content: "\f04d";
}

.fa-stop-circle:before {
  content: "\f28d";
}

.fa-stopwatch:before {
  content: "\f2f2";
}

.fa-store:before {
  content: "\f54e";
}

.fa-store-alt:before {
  content: "\f54f";
}

.fa-strava:before {
  content: "\f428";
}

.fa-stream:before {
  content: "\f550";
}

.fa-street-view:before {
  content: "\f21d";
}

.fa-strikethrough:before {
  content: "\f0cc";
}

.fa-stripe:before {
  content: "\f429";
}

.fa-stripe-s:before {
  content: "\f42a";
}

.fa-stroopwafel:before {
  content: "\f551";
}

.fa-studiovinari:before {
  content: "\f3f8";
}

.fa-stumbleupon:before {
  content: "\f1a4";
}

.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

.fa-subscript:before {
  content: "\f12c";
}

.fa-subway:before {
  content: "\f239";
}

.fa-suitcase:before {
  content: "\f0f2";
}

.fa-suitcase-rolling:before {
  content: "\f5c1";
}

.fa-sun:before {
  content: "\f185";
}

.fa-superpowers:before {
  content: "\f2dd";
}

.fa-superscript:before {
  content: "\f12b";
}

.fa-supple:before {
  content: "\f3f9";
}

.fa-surprise:before {
  content: "\f5c2";
}

.fa-suse:before {
  content: "\f7d6";
}

.fa-swatchbook:before {
  content: "\f5c3";
}

.fa-swimmer:before {
  content: "\f5c4";
}

.fa-swimming-pool:before {
  content: "\f5c5";
}

.fa-symfony:before {
  content: "\f83d";
}

.fa-synagogue:before {
  content: "\f69b";
}

.fa-sync:before {
  content: "\f021";
}

.fa-sync-alt:before {
  content: "\f2f1";
}

.fa-syringe:before {
  content: "\f48e";
}

.fa-table:before {
  content: "\f0ce";
}

.fa-table-tennis:before {
  content: "\f45d";
}

.fa-tablet:before {
  content: "\f10a";
}

.fa-tablet-alt:before {
  content: "\f3fa";
}

.fa-tablets:before {
  content: "\f490";
}

.fa-tachometer-alt:before {
  content: "\f3fd";
}

.fa-tag:before {
  content: "\f02b";
}

.fa-tags:before {
  content: "\f02c";
}

.fa-tape:before {
  content: "\f4db";
}

.fa-tasks:before {
  content: "\f0ae";
}

.fa-taxi:before {
  content: "\f1ba";
}

.fa-teamspeak:before {
  content: "\f4f9";
}

.fa-teeth:before {
  content: "\f62e";
}

.fa-teeth-open:before {
  content: "\f62f";
}

.fa-telegram:before {
  content: "\f2c6";
}

.fa-telegram-plane:before {
  content: "\f3fe";
}

.fa-temperature-high:before {
  content: "\f769";
}

.fa-temperature-low:before {
  content: "\f76b";
}

.fa-tencent-weibo:before {
  content: "\f1d5";
}

.fa-tenge:before {
  content: "\f7d7";
}

.fa-terminal:before {
  content: "\f120";
}

.fa-text-height:before {
  content: "\f034";
}

.fa-text-width:before {
  content: "\f035";
}

.fa-th:before {
  content: "\f00a";
}

.fa-th-large:before {
  content: "\f009";
}

.fa-th-list:before {
  content: "\f00b";
}

.fa-the-red-yeti:before {
  content: "\f69d";
}

.fa-theater-masks:before {
  content: "\f630";
}

.fa-themeco:before {
  content: "\f5c6";
}

.fa-themeisle:before {
  content: "\f2b2";
}

.fa-thermometer:before {
  content: "\f491";
}

.fa-thermometer-empty:before {
  content: "\f2cb";
}

.fa-thermometer-full:before {
  content: "\f2c7";
}

.fa-thermometer-half:before {
  content: "\f2c9";
}

.fa-thermometer-quarter:before {
  content: "\f2ca";
}

.fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

.fa-think-peaks:before {
  content: "\f731";
}

.fa-thumbs-down:before {
  content: "\f165";
}

.fa-thumbs-up:before {
  content: "\f164";
}

.fa-thumbtack:before {
  content: "\f08d";
}

.fa-ticket-alt:before {
  content: "\f3ff";
}

.fa-times:before {
  content: "\f00d";
}

.fa-times-circle:before {
  content: "\f057";
}

.fa-tint:before {
  content: "\f043";
}

.fa-tint-slash:before {
  content: "\f5c7";
}

.fa-tired:before {
  content: "\f5c8";
}

.fa-toggle-off:before {
  content: "\f204";
}

.fa-toggle-on:before {
  content: "\f205";
}

.fa-toilet:before {
  content: "\f7d8";
}

.fa-toilet-paper:before {
  content: "\f71e";
}

.fa-toolbox:before {
  content: "\f552";
}

.fa-tools:before {
  content: "\f7d9";
}

.fa-tooth:before {
  content: "\f5c9";
}

.fa-torah:before {
  content: "\f6a0";
}

.fa-torii-gate:before {
  content: "\f6a1";
}

.fa-tractor:before {
  content: "\f722";
}

.fa-trade-federation:before {
  content: "\f513";
}

.fa-trademark:before {
  content: "\f25c";
}

.fa-traffic-light:before {
  content: "\f637";
}

.fa-train:before {
  content: "\f238";
}

.fa-tram:before {
  content: "\f7da";
}

.fa-transgender:before {
  content: "\f224";
}

.fa-transgender-alt:before {
  content: "\f225";
}

.fa-trash:before {
  content: "\f1f8";
}

.fa-trash-alt:before {
  content: "\f2ed";
}

.fa-trash-restore:before {
  content: "\f829";
}

.fa-trash-restore-alt:before {
  content: "\f82a";
}

.fa-tree:before {
  content: "\f1bb";
}

.fa-trello:before {
  content: "\f181";
}

.fa-tripadvisor:before {
  content: "\f262";
}

.fa-trophy:before {
  content: "\f091";
}

.fa-truck:before {
  content: "\f0d1";
}

.fa-truck-loading:before {
  content: "\f4de";
}

.fa-truck-monster:before {
  content: "\f63b";
}

.fa-truck-moving:before {
  content: "\f4df";
}

.fa-truck-pickup:before {
  content: "\f63c";
}

.fa-tshirt:before {
  content: "\f553";
}

.fa-tty:before {
  content: "\f1e4";
}

.fa-tumblr:before {
  content: "\f173";
}

.fa-tumblr-square:before {
  content: "\f174";
}

.fa-tv:before {
  content: "\f26c";
}

.fa-twitch:before {
  content: "\f1e8";
}

.fa-twitter:before {
  content: "\f099";
}

.fa-twitter-square:before {
  content: "\f081";
}

.fa-typo3:before {
  content: "\f42b";
}

.fa-uber:before {
  content: "\f402";
}

.fa-ubuntu:before {
  content: "\f7df";
}

.fa-uikit:before {
  content: "\f403";
}

.fa-umbrella:before {
  content: "\f0e9";
}

.fa-umbrella-beach:before {
  content: "\f5ca";
}

.fa-underline:before {
  content: "\f0cd";
}

.fa-undo:before {
  content: "\f0e2";
}

.fa-undo-alt:before {
  content: "\f2ea";
}

.fa-uniregistry:before {
  content: "\f404";
}

.fa-universal-access:before {
  content: "\f29a";
}

.fa-university:before {
  content: "\f19c";
}

.fa-unlink:before {
  content: "\f127";
}

.fa-unlock:before {
  content: "\f09c";
}

.fa-unlock-alt:before {
  content: "\f13e";
}

.fa-untappd:before {
  content: "\f405";
}

.fa-upload:before {
  content: "\f093";
}

.fa-ups:before {
  content: "\f7e0";
}

.fa-usb:before {
  content: "\f287";
}

.fa-user:before {
  content: "\f007";
}

.fa-user-alt:before {
  content: "\f406";
}

.fa-user-alt-slash:before {
  content: "\f4fa";
}

.fa-user-astronaut:before {
  content: "\f4fb";
}

.fa-user-check:before {
  content: "\f4fc";
}

.fa-user-circle:before {
  content: "\f2bd";
}

.fa-user-clock:before {
  content: "\f4fd";
}

.fa-user-cog:before {
  content: "\f4fe";
}

.fa-user-edit:before {
  content: "\f4ff";
}

.fa-user-friends:before {
  content: "\f500";
}

.fa-user-graduate:before {
  content: "\f501";
}

.fa-user-injured:before {
  content: "\f728";
}

.fa-user-lock:before {
  content: "\f502";
}

.fa-user-md:before {
  content: "\f0f0";
}

.fa-user-minus:before {
  content: "\f503";
}

.fa-user-ninja:before {
  content: "\f504";
}

.fa-user-nurse:before {
  content: "\f82f";
}

.fa-user-plus:before {
  content: "\f234";
}

.fa-user-secret:before {
  content: "\f21b";
}

.fa-user-shield:before {
  content: "\f505";
}

.fa-user-slash:before {
  content: "\f506";
}

.fa-user-tag:before {
  content: "\f507";
}

.fa-user-tie:before {
  content: "\f508";
}

.fa-user-times:before {
  content: "\f235";
}

.fa-users:before {
  content: "\f0c0";
}

.fa-users-cog:before {
  content: "\f509";
}

.fa-usps:before {
  content: "\f7e1";
}

.fa-ussunnah:before {
  content: "\f407";
}

.fa-utensil-spoon:before {
  content: "\f2e5";
}

.fa-utensils:before {
  content: "\f2e7";
}

.fa-vaadin:before {
  content: "\f408";
}

.fa-vector-square:before {
  content: "\f5cb";
}

.fa-venus:before {
  content: "\f221";
}

.fa-venus-double:before {
  content: "\f226";
}

.fa-venus-mars:before {
  content: "\f228";
}

.fa-viacoin:before {
  content: "\f237";
}

.fa-viadeo:before {
  content: "\f2a9";
}

.fa-viadeo-square:before {
  content: "\f2aa";
}

.fa-vial:before {
  content: "\f492";
}

.fa-vials:before {
  content: "\f493";
}

.fa-viber:before {
  content: "\f409";
}

.fa-video:before {
  content: "\f03d";
}

.fa-video-slash:before {
  content: "\f4e2";
}

.fa-vihara:before {
  content: "\f6a7";
}

.fa-vimeo:before {
  content: "\f40a";
}

.fa-vimeo-square:before {
  content: "\f194";
}

.fa-vimeo-v:before {
  content: "\f27d";
}

.fa-vine:before {
  content: "\f1ca";
}

.fa-vk:before {
  content: "\f189";
}

.fa-vnv:before {
  content: "\f40b";
}

.fa-voicemail:before {
  content: "\f897";
}

.fa-volleyball-ball:before {
  content: "\f45f";
}

.fa-volume-down:before {
  content: "\f027";
}

.fa-volume-mute:before {
  content: "\f6a9";
}

.fa-volume-off:before {
  content: "\f026";
}

.fa-volume-up:before {
  content: "\f028";
}

.fa-vote-yea:before {
  content: "\f772";
}

.fa-vr-cardboard:before {
  content: "\f729";
}

.fa-vuejs:before {
  content: "\f41f";
}

.fa-walking:before {
  content: "\f554";
}

.fa-wallet:before {
  content: "\f555";
}

.fa-warehouse:before {
  content: "\f494";
}

.fa-water:before {
  content: "\f773";
}

.fa-wave-square:before {
  content: "\f83e";
}

.fa-waze:before {
  content: "\f83f";
}

.fa-weebly:before {
  content: "\f5cc";
}

.fa-weibo:before {
  content: "\f18a";
}

.fa-weight:before {
  content: "\f496";
}

.fa-weight-hanging:before {
  content: "\f5cd";
}

.fa-weixin:before {
  content: "\f1d7";
}

.fa-whatsapp:before {
  content: "\f232";
}

.fa-whatsapp-square:before {
  content: "\f40c";
}

.fa-wheelchair:before {
  content: "\f193";
}

.fa-whmcs:before {
  content: "\f40d";
}

.fa-wifi:before {
  content: "\f1eb";
}

.fa-wikipedia-w:before {
  content: "\f266";
}

.fa-wind:before {
  content: "\f72e";
}

.fa-window-close:before {
  content: "\f410";
}

.fa-window-maximize:before {
  content: "\f2d0";
}

.fa-window-minimize:before {
  content: "\f2d1";
}

.fa-window-restore:before {
  content: "\f2d2";
}

.fa-windows:before {
  content: "\f17a";
}

.fa-wine-bottle:before {
  content: "\f72f";
}

.fa-wine-glass:before {
  content: "\f4e3";
}

.fa-wine-glass-alt:before {
  content: "\f5ce";
}

.fa-wix:before {
  content: "\f5cf";
}

.fa-wizards-of-the-coast:before {
  content: "\f730";
}

.fa-wolf-pack-battalion:before {
  content: "\f514";
}

.fa-won-sign:before {
  content: "\f159";
}

.fa-wordpress:before {
  content: "\f19a";
}

.fa-wordpress-simple:before {
  content: "\f411";
}

.fa-wpbeginner:before {
  content: "\f297";
}

.fa-wpexplorer:before {
  content: "\f2de";
}

.fa-wpforms:before {
  content: "\f298";
}

.fa-wpressr:before {
  content: "\f3e4";
}

.fa-wrench:before {
  content: "\f0ad";
}

.fa-x-ray:before {
  content: "\f497";
}

.fa-xbox:before {
  content: "\f412";
}

.fa-xing:before {
  content: "\f168";
}

.fa-xing-square:before {
  content: "\f169";
}

.fa-y-combinator:before {
  content: "\f23b";
}

.fa-yahoo:before {
  content: "\f19e";
}

.fa-yammer:before {
  content: "\f840";
}

.fa-yandex:before {
  content: "\f413";
}

.fa-yandex-international:before {
  content: "\f414";
}

.fa-yarn:before {
  content: "\f7e3";
}

.fa-yelp:before {
  content: "\f1e9";
}

.fa-yen-sign:before {
  content: "\f157";
}

.fa-yin-yang:before {
  content: "\f6ad";
}

.fa-yoast:before {
  content: "\f2b1";
}

.fa-youtube:before {
  content: "\f167";
}

.fa-youtube-square:before {
  content: "\f431";
}

.fa-zhihu:before {
  content: "\f63f";
}

.sr-only {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

/*!
 * Font Awesome Free 5.10.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 400;
  font-display: auto;
  src: url("fonts/webfonts/fa-regular-400.eot");
  src: url("fonts/webfonts/fa-regular-400.eot?#iefix") format("embedded-opentype"), url("fonts/webfonts/fa-regular-400.woff2") format("woff2"), url("fonts/webfonts/fa-regular-400.woff") format("woff"), url("fonts/webfonts/fa-regular-400.ttf") format("truetype"), url("fonts/webfonts/fa-regular-400.svg#fontawesome") format("svg");
}

.far {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
}

/*!
 * Font Awesome Free 5.10.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@font-face {
  font-family: 'Font Awesome 5 Free Solid';
  font-style: normal;
  font-weight: 900;
  font-display: auto;
  src: url("fonts/webfonts/fa-solid-900.eot");
  src: url("fonts/webfonts/fa-solid-900.eot?#iefix") format("embedded-opentype"), url("fonts/webfonts/fa-solid-900.woff2") format("woff2"), url("fonts/webfonts/fa-solid-900.woff") format("woff"), url("fonts/webfonts/fa-solid-900.ttf") format("truetype"), url("fonts/webfonts/fa-solid-900.svg#fontawesome") format("svg");
}

.fa,
.fas {
  font-family: 'Font Awesome 5 Free Solid';
  font-weight: 900;
}

/*!
 * Font Awesome Free 5.10.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@font-face {
  font-family: 'Font Awesome 5 Brands';
  font-style: normal;
  font-weight: normal;
  font-display: auto;
  src: url("fonts/webfonts/fa-brands-400.eot");
  src: url("fonts/webfonts/fa-brands-400.eot?#iefix") format("embedded-opentype"), url("fonts/webfonts/fa-brands-400.woff2") format("woff2"), url("fonts/webfonts/fa-brands-400.woff") format("woff"), url("fonts/webfonts/fa-brands-400.ttf") format("truetype"), url("fonts/webfonts/fa-brands-400.svg#fontawesome") format("svg");
}

.fab {
  font-family: 'Font Awesome 5 Brands';
}

/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
/* FONT PATH
 * -------------------------- */
@font-face {
  font-family: 'FontAwesome';
  src: url("fonts/webfonts/fontawesome-webfont.eot?v=5.10.1");
  src: url("fonts/webfonts/fontawesome-webfont.eot?#iefix&v=5.10.1") format("embedded-opentype"), url("fonts/webfonts/fontawesome-webfont.woff2?v=5.10.1") format("woff2"), url("fonts/webfonts/fontawesome-webfont.woff?v=5.10.1") format("woff"), url("fonts/webfonts/fontawesome-webfont.ttf?v=5.10.1") format("truetype"), url("fonts/webfonts/fontawesome-webfont.svg?v=5.10.1#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}

.fa {
  display: inline-block;
  font: normal normal normal 16px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* makes the font 33% larger relative to the icon container */
.fa-lg {
  font-size: 1.33333em;
  line-height: 0.75em;
  vertical-align: -15%;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-fw {
  width: 1.28571em;
  text-align: center;
}

.fa-ul {
  padding-left: 0;
  margin-left: 2em;
  list-style-type: none;
}

.fa-ul > li {
  position: relative;
}

.fa-li {
  position: absolute;
  left: -2em;
  width: 2em;
  top: 0.14286em;
  text-align: center;
}

.fa-li.fa-lg {
  left: -1.71429em;
}

.fa-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eee;
  border-radius: .1em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left {
  margin-right: .3em;
}

.fa.fa-pull-right {
  margin-left: .3em;
}

/* Deprecated as of 4.4.0 */
.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.fa.pull-left {
  margin-right: .3em;
}

.fa.pull-right {
  margin-left: .3em;
}

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  transform: rotate(90deg);
}

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  transform: rotate(180deg);
}

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  transform: scale(1, -1);
}

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  -webkit-filter: none;
          filter: none;
}

.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

.fa-stack-1x,
.fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: #fff;
}

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.fa-glass:before {
  content: "";
}

.fa-music:before {
  content: "";
}

.fa-search:before {
  content: "";
}

.fa-envelope-o:before {
  content: "";
}

.fa-heart:before {
  content: "";
}

.fa-star:before {
  content: "";
}

.fa-star-o:before {
  content: "";
}

.fa-user:before {
  content: "";
}

.fa-film:before {
  content: "";
}

.fa-th-large:before {
  content: "";
}

.fa-th:before {
  content: "";
}

.fa-th-list:before {
  content: "";
}

.fa-check:before {
  content: "";
}

.fa-remove:before,
.fa-close:before,
.fa-times:before {
  content: "";
}

.fa-search-plus:before {
  content: "";
}

.fa-search-minus:before {
  content: "";
}

.fa-power-off:before {
  content: "";
}

.fa-signal:before {
  content: "";
}

.fa-gear:before,
.fa-cog:before {
  content: "";
}

.fa-trash-o:before {
  content: "";
}

.fa-home:before {
  content: "";
}

.fa-file-o:before {
  content: "";
}

.fa-clock-o:before {
  content: "";
}

.fa-road:before {
  content: "";
}

.fa-download:before {
  content: "";
}

.fa-arrow-circle-o-down:before {
  content: "";
}

.fa-arrow-circle-o-up:before {
  content: "";
}

.fa-inbox:before {
  content: "";
}

.fa-play-circle-o:before {
  content: "";
}

.fa-rotate-right:before,
.fa-repeat:before {
  content: "";
}

.fa-refresh:before {
  content: "";
}

.fa-list-alt:before {
  content: "";
}

.fa-lock:before {
  content: "";
}

.fa-flag:before {
  content: "";
}

.fa-headphones:before {
  content: "";
}

.fa-volume-off:before {
  content: "";
}

.fa-volume-down:before {
  content: "";
}

.fa-volume-up:before {
  content: "";
}

.fa-qrcode:before {
  content: "";
}

.fa-barcode:before {
  content: "";
}

.fa-tag:before {
  content: "";
}

.fa-tags:before {
  content: "";
}

.fa-book:before {
  content: "";
}

.fa-bookmark:before {
  content: "";
}

.fa-print:before {
  content: "";
}

.fa-camera:before {
  content: "";
}

.fa-font:before {
  content: "";
}

.fa-bold:before {
  content: "";
}

.fa-italic:before {
  content: "";
}

.fa-text-height:before {
  content: "";
}

.fa-text-width:before {
  content: "";
}

.fa-align-left:before {
  content: "";
}

.fa-align-center:before {
  content: "";
}

.fa-align-right:before {
  content: "";
}

.fa-align-justify:before {
  content: "";
}

.fa-list:before {
  content: "";
}

.fa-dedent:before,
.fa-outdent:before {
  content: "";
}

.fa-indent:before {
  content: "";
}

.fa-video-camera:before {
  content: "";
}

.fa-photo:before,
.fa-image:before,
.fa-picture-o:before {
  content: "";
}

.fa-pencil:before {
  content: "";
}

.fa-map-marker:before {
  content: "";
}

.fa-adjust:before {
  content: "";
}

.fa-tint:before {
  content: "";
}

.fa-edit:before,
.fa-pencil-square-o:before {
  content: "";
}

.fa-share-square-o:before {
  content: "";
}

.fa-check-square-o:before {
  content: "";
}

.fa-arrows:before {
  content: "";
}

.fa-step-backward:before {
  content: "";
}

.fa-fast-backward:before {
  content: "";
}

.fa-backward:before {
  content: "";
}

.fa-play:before {
  content: "";
}

.fa-pause:before {
  content: "";
}

.fa-stop:before {
  content: "";
}

.fa-forward:before {
  content: "";
}

.fa-fast-forward:before {
  content: "";
}

.fa-step-forward:before {
  content: "";
}

.fa-eject:before {
  content: "";
}

.fa-chevron-left:before {
  content: "";
}

.fa-chevron-right:before {
  content: "";
}

.fa-plus-circle:before {
  content: "";
}

.fa-minus-circle:before {
  content: "";
}

.fa-times-circle:before {
  content: "";
}

.fa-check-circle:before {
  content: "";
}

.fa-question-circle:before {
  content: "";
}

.fa-info-circle:before {
  content: "";
}

.fa-crosshairs:before {
  content: "";
}

.fa-times-circle-o:before {
  content: "";
}

.fa-check-circle-o:before {
  content: "";
}

.fa-ban:before {
  content: "";
}

.fa-arrow-left:before {
  content: "";
}

.fa-arrow-right:before {
  content: "";
}

.fa-arrow-up:before {
  content: "";
}

.fa-arrow-down:before {
  content: "";
}

.fa-mail-forward:before,
.fa-share:before {
  content: "";
}

.fa-expand:before {
  content: "";
}

.fa-compress:before {
  content: "";
}

.fa-plus:before {
  content: "";
}

.fa-minus:before {
  content: "";
}

.fa-asterisk:before {
  content: "";
}

.fa-exclamation-circle:before {
  content: "";
}

.fa-gift:before {
  content: "";
}

.fa-leaf:before {
  content: "";
}

.fa-fire:before {
  content: "";
}

.fa-eye:before {
  content: "";
}

.fa-eye-slash:before {
  content: "";
}

.fa-warning:before,
.fa-exclamation-triangle:before {
  content: "";
}

.fa-plane:before {
  content: "";
}

.fa-calendar:before {
  content: "";
}

.fa-random:before {
  content: "";
}

.fa-comment:before {
  content: "";
}

.fa-magnet:before {
  content: "";
}

.fa-chevron-up:before {
  content: "";
}

.fa-chevron-down:before {
  content: "";
}

.fa-retweet:before {
  content: "";
}

.fa-shopping-cart:before {
  content: "";
}

.fa-folder:before {
  content: "";
}

.fa-folder-open:before {
  content: "";
}

.fa-arrows-v:before {
  content: "";
}

.fa-arrows-h:before {
  content: "";
}

.fa-bar-chart-o:before,
.fa-bar-chart:before {
  content: "";
}

.fa-twitter-square:before {
  content: "";
}

.fa-facebook-square:before {
  content: "";
}

.fa-camera-retro:before {
  content: "";
}

.fa-key:before {
  content: "";
}

.fa-gears:before,
.fa-cogs:before {
  content: "";
}

.fa-comments:before {
  content: "";
}

.fa-thumbs-o-up:before {
  content: "";
}

.fa-thumbs-o-down:before {
  content: "";
}

.fa-star-half:before {
  content: "";
}

.fa-heart-o:before {
  content: "";
}

.fa-sign-out:before {
  content: "";
}

.fa-linkedin-square:before {
  content: "";
}

.fa-thumb-tack:before {
  content: "";
}

.fa-external-link:before {
  content: "";
}

.fa-sign-in:before {
  content: "";
}

.fa-trophy:before {
  content: "";
}

.fa-github-square:before {
  content: "";
}

.fa-upload:before {
  content: "";
}

.fa-lemon-o:before {
  content: "";
}

.fa-phone:before {
  content: "";
}

.fa-square-o:before {
  content: "";
}

.fa-bookmark-o:before {
  content: "";
}

.fa-phone-square:before {
  content: "";
}

.fa-twitter:before {
  content: "";
}

.fa-facebook-f:before,
.fa-facebook:before {
  content: "";
}

.fa-github:before {
  content: "";
}

.fa-unlock:before {
  content: "";
}

.fa-credit-card:before {
  content: "";
}

.fa-feed:before,
.fa-rss:before {
  content: "";
}

.fa-hdd-o:before {
  content: "";
}

.fa-bullhorn:before {
  content: "";
}

.fa-bell:before {
  content: "";
}

.fa-certificate:before {
  content: "";
}

.fa-hand-o-right:before {
  content: "";
}

.fa-hand-o-left:before {
  content: "";
}

.fa-hand-o-up:before {
  content: "";
}

.fa-hand-o-down:before {
  content: "";
}

.fa-arrow-circle-left:before {
  content: "";
}

.fa-arrow-circle-right:before {
  content: "";
}

.fa-arrow-circle-up:before {
  content: "";
}

.fa-arrow-circle-down:before {
  content: "";
}

.fa-globe:before {
  content: "";
}

.fa-wrench:before {
  content: "";
}

.fa-tasks:before {
  content: "";
}

.fa-filter:before {
  content: "";
}

.fa-briefcase:before {
  content: "";
}

.fa-arrows-alt:before {
  content: "";
}

.fa-group:before,
.fa-users:before {
  content: "";
}

.fa-chain:before,
.fa-link:before {
  content: "";
}

.fa-cloud:before {
  content: "";
}

.fa-flask:before {
  content: "";
}

.fa-cut:before,
.fa-scissors:before {
  content: "";
}

.fa-copy:before,
.fa-files-o:before {
  content: "";
}

.fa-paperclip:before {
  content: "";
}

.fa-save:before,
.fa-floppy-o:before {
  content: "";
}

.fa-square:before {
  content: "";
}

.fa-navicon:before,
.fa-reorder:before,
.fa-bars:before {
  content: "";
}

.fa-list-ul:before {
  content: "";
}

.fa-list-ol:before {
  content: "";
}

.fa-strikethrough:before {
  content: "";
}

.fa-underline:before {
  content: "";
}

.fa-table:before {
  content: "";
}

.fa-magic:before {
  content: "";
}

.fa-truck:before {
  content: "";
}

.fa-pinterest:before {
  content: "";
}

.fa-pinterest-square:before {
  content: "";
}

.fa-google-plus-square:before {
  content: "";
}

.fa-google-plus:before {
  content: "";
}

.fa-money:before {
  content: "";
}

.fa-caret-down:before {
  content: "";
}

.fa-caret-up:before {
  content: "";
}

.fa-caret-left:before {
  content: "";
}

.fa-caret-right:before {
  content: "";
}

.fa-columns:before {
  content: "";
}

.fa-unsorted:before,
.fa-sort:before {
  content: "";
}

.fa-sort-down:before,
.fa-sort-desc:before {
  content: "";
}

.fa-sort-up:before,
.fa-sort-asc:before {
  content: "";
}

.fa-envelope:before {
  content: "";
}

.fa-linkedin:before {
  content: "";
}

.fa-rotate-left:before,
.fa-undo:before {
  content: "";
}

.fa-legal:before,
.fa-gavel:before {
  content: "";
}

.fa-dashboard:before,
.fa-tachometer:before {
  content: "";
}

.fa-comment-o:before {
  content: "";
}

.fa-comments-o:before {
  content: "";
}

.fa-flash:before,
.fa-bolt:before {
  content: "";
}

.fa-sitemap:before {
  content: "";
}

.fa-umbrella:before {
  content: "";
}

.fa-paste:before,
.fa-clipboard:before {
  content: "";
}

.fa-lightbulb-o:before {
  content: "";
}

.fa-exchange:before {
  content: "";
}

.fa-cloud-download:before {
  content: "";
}

.fa-cloud-upload:before {
  content: "";
}

.fa-user-md:before {
  content: "";
}

.fa-stethoscope:before {
  content: "";
}

.fa-suitcase:before {
  content: "";
}

.fa-bell-o:before {
  content: "";
}

.fa-coffee:before {
  content: "";
}

.fa-cutlery:before {
  content: "";
}

.fa-file-text-o:before {
  content: "";
}

.fa-building-o:before {
  content: "";
}

.fa-hospital-o:before {
  content: "";
}

.fa-ambulance:before {
  content: "";
}

.fa-medkit:before {
  content: "";
}

.fa-fighter-jet:before {
  content: "";
}

.fa-beer:before {
  content: "";
}

.fa-h-square:before {
  content: "";
}

.fa-plus-square:before {
  content: "";
}

.fa-angle-double-left:before {
  content: "";
}

.fa-angle-double-right:before {
  content: "";
}

.fa-angle-double-up:before {
  content: "";
}

.fa-angle-double-down:before {
  content: "";
}

.fa-angle-left:before {
  content: "";
}

.fa-angle-right:before {
  content: "";
}

.fa-angle-up:before {
  content: "";
}

.fa-angle-down:before {
  content: "";
}

.fa-desktop:before {
  content: "";
}

.fa-laptop:before {
  content: "";
}

.fa-tablet:before {
  content: "";
}

.fa-mobile-phone:before,
.fa-mobile:before {
  content: "";
}

.fa-circle-o:before {
  content: "";
}

.fa-quote-left:before {
  content: "";
}

.fa-quote-right:before {
  content: "";
}

.fa-spinner:before {
  content: "";
}

.fa-circle:before {
  content: "";
}

.fa-mail-reply:before,
.fa-reply:before {
  content: "";
}

.fa-github-alt:before {
  content: "";
}

.fa-folder-o:before {
  content: "";
}

.fa-folder-open-o:before {
  content: "";
}

.fa-smile-o:before {
  content: "";
}

.fa-frown-o:before {
  content: "";
}

.fa-meh-o:before {
  content: "";
}

.fa-gamepad:before {
  content: "";
}

.fa-keyboard-o:before {
  content: "";
}

.fa-flag-o:before {
  content: "";
}

.fa-flag-checkered:before {
  content: "";
}

.fa-terminal:before {
  content: "";
}

.fa-code:before {
  content: "";
}

.fa-mail-reply-all:before,
.fa-reply-all:before {
  content: "";
}

.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
  content: "";
}

.fa-location-arrow:before {
  content: "";
}

.fa-crop:before {
  content: "";
}

.fa-code-fork:before {
  content: "";
}

.fa-unlink:before,
.fa-chain-broken:before {
  content: "";
}

.fa-question:before {
  content: "";
}

.fa-info:before {
  content: "";
}

.fa-exclamation:before {
  content: "";
}

.fa-superscript:before {
  content: "";
}

.fa-subscript:before {
  content: "";
}

.fa-eraser:before {
  content: "";
}

.fa-puzzle-piece:before {
  content: "";
}

.fa-microphone:before {
  content: "";
}

.fa-microphone-slash:before {
  content: "";
}

.fa-shield:before {
  content: "";
}

.fa-calendar-o:before {
  content: "";
}

.fa-fire-extinguisher:before {
  content: "";
}

.fa-rocket:before {
  content: "";
}

.fa-maxcdn:before {
  content: "";
}

.fa-chevron-circle-left:before {
  content: "";
}

.fa-chevron-circle-right:before {
  content: "";
}

.fa-chevron-circle-up:before {
  content: "";
}

.fa-chevron-circle-down:before {
  content: "";
}

.fa-html5:before {
  content: "";
}

.fa-css3:before {
  content: "";
}

.fa-anchor:before {
  content: "";
}

.fa-unlock-alt:before {
  content: "";
}

.fa-bullseye:before {
  content: "";
}

.fa-ellipsis-h:before {
  content: "";
}

.fa-ellipsis-v:before {
  content: "";
}

.fa-rss-square:before {
  content: "";
}

.fa-play-circle:before {
  content: "";
}

.fa-ticket:before {
  content: "";
}

.fa-minus-square:before {
  content: "";
}

.fa-minus-square-o:before {
  content: "";
}

.fa-level-up:before {
  content: "";
}

.fa-level-down:before {
  content: "";
}

.fa-check-square:before {
  content: "";
}

.fa-pencil-square:before {
  content: "";
}

.fa-external-link-square:before {
  content: "";
}

.fa-share-square:before {
  content: "";
}

.fa-compass:before {
  content: "";
}

.fa-toggle-down:before,
.fa-caret-square-o-down:before {
  content: "";
}

.fa-toggle-up:before,
.fa-caret-square-o-up:before {
  content: "";
}

.fa-toggle-right:before,
.fa-caret-square-o-right:before {
  content: "";
}

.fa-euro:before,
.fa-eur:before {
  content: "";
}

.fa-gbp:before {
  content: "";
}

.fa-dollar:before,
.fa-usd:before {
  content: "";
}

.fa-rupee:before,
.fa-inr:before {
  content: "";
}

.fa-cny:before,
.fa-rmb:before,
.fa-yen:before,
.fa-jpy:before {
  content: "";
}

.fa-ruble:before,
.fa-rouble:before,
.fa-rub:before {
  content: "";
}

.fa-won:before,
.fa-krw:before {
  content: "";
}

.fa-bitcoin:before,
.fa-btc:before {
  content: "";
}

.fa-file:before {
  content: "";
}

.fa-file-text:before {
  content: "";
}

.fa-sort-alpha-asc:before {
  content: "";
}

.fa-sort-alpha-desc:before {
  content: "";
}

.fa-sort-amount-asc:before {
  content: "";
}

.fa-sort-amount-desc:before {
  content: "";
}

.fa-sort-numeric-asc:before {
  content: "";
}

.fa-sort-numeric-desc:before {
  content: "";
}

.fa-thumbs-up:before {
  content: "";
}

.fa-thumbs-down:before {
  content: "";
}

.fa-youtube-square:before {
  content: "";
}

.fa-youtube:before {
  content: "";
}

.fa-xing:before {
  content: "";
}

.fa-xing-square:before {
  content: "";
}

.fa-youtube-play:before {
  content: "";
}

.fa-dropbox:before {
  content: "";
}

.fa-stack-overflow:before {
  content: "";
}

.fa-instagram:before {
  content: "";
}

.fa-flickr:before {
  content: "";
}

.fa-adn:before {
  content: "";
}

.fa-bitbucket:before {
  content: "";
}

.fa-bitbucket-square:before {
  content: "";
}

.fa-tumblr:before {
  content: "";
}

.fa-tumblr-square:before {
  content: "";
}

.fa-long-arrow-down:before {
  content: "";
}

.fa-long-arrow-up:before {
  content: "";
}

.fa-long-arrow-left:before {
  content: "";
}

.fa-long-arrow-right:before {
  content: "";
}

.fa-apple:before {
  content: "";
}

.fa-windows:before {
  content: "";
}

.fa-android:before {
  content: "";
}

.fa-linux:before {
  content: "";
}

.fa-dribbble:before {
  content: "";
}

.fa-skype:before {
  content: "";
}

.fa-foursquare:before {
  content: "";
}

.fa-trello:before {
  content: "";
}

.fa-female:before {
  content: "";
}

.fa-male:before {
  content: "";
}

.fa-gittip:before,
.fa-gratipay:before {
  content: "";
}

.fa-sun-o:before {
  content: "";
}

.fa-moon-o:before {
  content: "";
}

.fa-archive:before {
  content: "";
}

.fa-bug:before {
  content: "";
}

.fa-vk:before {
  content: "";
}

.fa-weibo:before {
  content: "";
}

.fa-renren:before {
  content: "";
}

.fa-pagelines:before {
  content: "";
}

.fa-stack-exchange:before {
  content: "";
}

.fa-arrow-circle-o-right:before {
  content: "";
}

.fa-arrow-circle-o-left:before {
  content: "";
}

.fa-toggle-left:before,
.fa-caret-square-o-left:before {
  content: "";
}

.fa-dot-circle-o:before {
  content: "";
}

.fa-wheelchair:before {
  content: "";
}

.fa-vimeo-square:before {
  content: "";
}

.fa-turkish-lira:before,
.fa-try:before {
  content: "";
}

.fa-plus-square-o:before {
  content: "";
}

.fa-space-shuttle:before {
  content: "";
}

.fa-slack:before {
  content: "";
}

.fa-envelope-square:before {
  content: "";
}

.fa-wordpress:before {
  content: "";
}

.fa-openid:before {
  content: "";
}

.fa-institution:before,
.fa-bank:before,
.fa-university:before {
  content: "";
}

.fa-mortar-board:before,
.fa-graduation-cap:before {
  content: "";
}

.fa-yahoo:before {
  content: "";
}

.fa-google:before {
  content: "";
}

.fa-reddit:before {
  content: "";
}

.fa-reddit-square:before {
  content: "";
}

.fa-stumbleupon-circle:before {
  content: "";
}

.fa-stumbleupon:before {
  content: "";
}

.fa-delicious:before {
  content: "";
}

.fa-digg:before {
  content: "";
}

.fa-pied-piper-pp:before {
  content: "";
}

.fa-pied-piper-alt:before {
  content: "";
}

.fa-drupal:before {
  content: "";
}

.fa-joomla:before {
  content: "";
}

.fa-language:before {
  content: "";
}

.fa-fax:before {
  content: "";
}

.fa-building:before {
  content: "";
}

.fa-child:before {
  content: "";
}

.fa-paw:before {
  content: "";
}

.fa-spoon:before {
  content: "";
}

.fa-cube:before {
  content: "";
}

.fa-cubes:before {
  content: "";
}

.fa-behance:before {
  content: "";
}

.fa-behance-square:before {
  content: "";
}

.fa-steam:before {
  content: "";
}

.fa-steam-square:before {
  content: "";
}

.fa-recycle:before {
  content: "";
}

.fa-automobile:before,
.fa-car:before {
  content: "";
}

.fa-cab:before,
.fa-taxi:before {
  content: "";
}

.fa-tree:before {
  content: "";
}

.fa-spotify:before {
  content: "";
}

.fa-deviantart:before {
  content: "";
}

.fa-soundcloud:before {
  content: "";
}

.fa-database:before {
  content: "";
}

.fa-file-pdf-o:before {
  content: "";
}

.fa-file-word-o:before {
  content: "";
}

.fa-file-excel-o:before {
  content: "";
}

.fa-file-powerpoint-o:before {
  content: "";
}

.fa-file-photo-o:before,
.fa-file-picture-o:before,
.fa-file-image-o:before {
  content: "";
}

.fa-file-zip-o:before,
.fa-file-archive-o:before {
  content: "";
}

.fa-file-sound-o:before,
.fa-file-audio-o:before {
  content: "";
}

.fa-file-movie-o:before,
.fa-file-video-o:before {
  content: "";
}

.fa-file-code-o:before {
  content: "";
}

.fa-vine:before {
  content: "";
}

.fa-codepen:before {
  content: "";
}

.fa-jsfiddle:before {
  content: "";
}

.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-saver:before,
.fa-support:before,
.fa-life-ring:before {
  content: "";
}

.fa-circle-o-notch:before {
  content: "";
}

.fa-ra:before,
.fa-resistance:before,
.fa-rebel:before {
  content: "";
}

.fa-ge:before,
.fa-empire:before {
  content: "";
}

.fa-git-square:before {
  content: "";
}

.fa-git:before {
  content: "";
}

.fa-y-combinator-square:before,
.fa-yc-square:before,
.fa-hacker-news:before {
  content: "";
}

.fa-tencent-weibo:before {
  content: "";
}

.fa-qq:before {
  content: "";
}

.fa-wechat:before,
.fa-weixin:before {
  content: "";
}

.fa-send:before,
.fa-paper-plane:before {
  content: "";
}

.fa-send-o:before,
.fa-paper-plane-o:before {
  content: "";
}

.fa-history:before {
  content: "";
}

.fa-circle-thin:before {
  content: "";
}

.fa-header:before {
  content: "";
}

.fa-paragraph:before {
  content: "";
}

.fa-sliders:before {
  content: "";
}

.fa-share-alt:before {
  content: "";
}

.fa-share-alt-square:before {
  content: "";
}

.fa-bomb:before {
  content: "";
}

.fa-soccer-ball-o:before,
.fa-futbol-o:before {
  content: "";
}

.fa-tty:before {
  content: "";
}

.fa-binoculars:before {
  content: "";
}

.fa-plug:before {
  content: "";
}

.fa-slideshare:before {
  content: "";
}

.fa-twitch:before {
  content: "";
}

.fa-yelp:before {
  content: "";
}

.fa-newspaper-o:before {
  content: "";
}

.fa-wifi:before {
  content: "";
}

.fa-calculator:before {
  content: "";
}

.fa-paypal:before {
  content: "";
}

.fa-google-wallet:before {
  content: "";
}

.fa-cc-visa:before {
  content: "";
}

.fa-cc-mastercard:before {
  content: "";
}

.fa-cc-discover:before {
  content: "";
}

.fa-cc-amex:before {
  content: "";
}

.fa-cc-paypal:before {
  content: "";
}

.fa-cc-stripe:before {
  content: "";
}

.fa-bell-slash:before {
  content: "";
}

.fa-bell-slash-o:before {
  content: "";
}

.fa-trash:before {
  content: "";
}

.fa-copyright:before {
  content: "";
}

.fa-at:before {
  content: "";
}

.fa-eyedropper:before {
  content: "";
}

.fa-paint-brush:before {
  content: "";
}

.fa-birthday-cake:before {
  content: "";
}

.fa-area-chart:before {
  content: "";
}

.fa-pie-chart:before {
  content: "";
}

.fa-line-chart:before {
  content: "";
}

.fa-lastfm:before {
  content: "";
}

.fa-lastfm-square:before {
  content: "";
}

.fa-toggle-off:before {
  content: "";
}

.fa-toggle-on:before {
  content: "";
}

.fa-bicycle:before {
  content: "";
}

.fa-bus:before {
  content: "";
}

.fa-ioxhost:before {
  content: "";
}

.fa-angellist:before {
  content: "";
}

.fa-cc:before {
  content: "";
}

.fa-shekel:before,
.fa-sheqel:before,
.fa-ils:before {
  content: "";
}

.fa-meanpath:before {
  content: "";
}

.fa-buysellads:before {
  content: "";
}

.fa-connectdevelop:before {
  content: "";
}

.fa-dashcube:before {
  content: "";
}

.fa-forumbee:before {
  content: "";
}

.fa-leanpub:before {
  content: "";
}

.fa-sellsy:before {
  content: "";
}

.fa-shirtsinbulk:before {
  content: "";
}

.fa-simplybuilt:before {
  content: "";
}

.fa-skyatlas:before {
  content: "";
}

.fa-cart-plus:before {
  content: "";
}

.fa-cart-arrow-down:before {
  content: "";
}

.fa-diamond:before {
  content: "";
}

.fa-ship:before {
  content: "";
}

.fa-user-secret:before {
  content: "";
}

.fa-motorcycle:before {
  content: "";
}

.fa-street-view:before {
  content: "";
}

.fa-heartbeat:before {
  content: "";
}

.fa-venus:before {
  content: "";
}

.fa-mars:before {
  content: "";
}

.fa-mercury:before {
  content: "";
}

.fa-intersex:before,
.fa-transgender:before {
  content: "";
}

.fa-transgender-alt:before {
  content: "";
}

.fa-venus-double:before {
  content: "";
}

.fa-mars-double:before {
  content: "";
}

.fa-venus-mars:before {
  content: "";
}

.fa-mars-stroke:before {
  content: "";
}

.fa-mars-stroke-v:before {
  content: "";
}

.fa-mars-stroke-h:before {
  content: "";
}

.fa-neuter:before {
  content: "";
}

.fa-genderless:before {
  content: "";
}

.fa-facebook-official:before {
  content: "";
}

.fa-pinterest-p:before {
  content: "";
}

.fa-whatsapp:before {
  content: "";
}

.fa-server:before {
  content: "";
}

.fa-user-plus:before {
  content: "";
}

.fa-user-times:before {
  content: "";
}

.fa-hotel:before,
.fa-bed:before {
  content: "";
}

.fa-viacoin:before {
  content: "";
}

.fa-train:before {
  content: "";
}

.fa-subway:before {
  content: "";
}

.fa-medium:before {
  content: "";
}

.fa-yc:before,
.fa-y-combinator:before {
  content: "";
}

.fa-optin-monster:before {
  content: "";
}

.fa-opencart:before {
  content: "";
}

.fa-expeditedssl:before {
  content: "";
}

.fa-battery-4:before,
.fa-battery:before,
.fa-battery-full:before {
  content: "";
}

.fa-battery-3:before,
.fa-battery-three-quarters:before {
  content: "";
}

.fa-battery-2:before,
.fa-battery-half:before {
  content: "";
}

.fa-battery-1:before,
.fa-battery-quarter:before {
  content: "";
}

.fa-battery-0:before,
.fa-battery-empty:before {
  content: "";
}

.fa-mouse-pointer:before {
  content: "";
}

.fa-i-cursor:before {
  content: "";
}

.fa-object-group:before {
  content: "";
}

.fa-object-ungroup:before {
  content: "";
}

.fa-sticky-note:before {
  content: "";
}

.fa-sticky-note-o:before {
  content: "";
}

.fa-cc-jcb:before {
  content: "";
}

.fa-cc-diners-club:before {
  content: "";
}

.fa-clone:before {
  content: "";
}

.fa-balance-scale:before {
  content: "";
}

.fa-hourglass-o:before {
  content: "";
}

.fa-hourglass-1:before,
.fa-hourglass-start:before {
  content: "";
}

.fa-hourglass-2:before,
.fa-hourglass-half:before {
  content: "";
}

.fa-hourglass-3:before,
.fa-hourglass-end:before {
  content: "";
}

.fa-hourglass:before {
  content: "";
}

.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
  content: "";
}

.fa-hand-stop-o:before,
.fa-hand-paper-o:before {
  content: "";
}

.fa-hand-scissors-o:before {
  content: "";
}

.fa-hand-lizard-o:before {
  content: "";
}

.fa-hand-spock-o:before {
  content: "";
}

.fa-hand-pointer-o:before {
  content: "";
}

.fa-hand-peace-o:before {
  content: "";
}

.fa-trademark:before {
  content: "";
}

.fa-registered:before {
  content: "";
}

.fa-creative-commons:before {
  content: "";
}

.fa-gg:before {
  content: "";
}

.fa-gg-circle:before {
  content: "";
}

.fa-tripadvisor:before {
  content: "";
}

.fa-odnoklassniki:before {
  content: "";
}

.fa-odnoklassniki-square:before {
  content: "";
}

.fa-get-pocket:before {
  content: "";
}

.fa-wikipedia-w:before {
  content: "";
}

.fa-safari:before {
  content: "";
}

.fa-chrome:before {
  content: "";
}

.fa-firefox:before {
  content: "";
}

.fa-opera:before {
  content: "";
}

.fa-internet-explorer:before {
  content: "";
}

.fa-tv:before,
.fa-television:before {
  content: "";
}

.fa-contao:before {
  content: "";
}

.fa-500px:before {
  content: "";
}

.fa-amazon:before {
  content: "";
}

.fa-calendar-plus-o:before {
  content: "";
}

.fa-calendar-minus-o:before {
  content: "";
}

.fa-calendar-times-o:before {
  content: "";
}

.fa-calendar-check-o:before {
  content: "";
}

.fa-industry:before {
  content: "";
}

.fa-map-pin:before {
  content: "";
}

.fa-map-signs:before {
  content: "";
}

.fa-map-o:before {
  content: "";
}

.fa-map:before {
  content: "";
}

.fa-commenting:before {
  content: "";
}

.fa-commenting-o:before {
  content: "";
}

.fa-houzz:before {
  content: "";
}

.fa-vimeo:before {
  content: "";
}

.fa-black-tie:before {
  content: "";
}

.fa-fonticons:before {
  content: "";
}

.fa-reddit-alien:before {
  content: "";
}

.fa-edge:before {
  content: "";
}

.fa-credit-card-alt:before {
  content: "";
}

.fa-codiepie:before {
  content: "";
}

.fa-modx:before {
  content: "";
}

.fa-fort-awesome:before {
  content: "";
}

.fa-usb:before {
  content: "";
}

.fa-product-hunt:before {
  content: "";
}

.fa-mixcloud:before {
  content: "";
}

.fa-scribd:before {
  content: "";
}

.fa-pause-circle:before {
  content: "";
}

.fa-pause-circle-o:before {
  content: "";
}

.fa-stop-circle:before {
  content: "";
}

.fa-stop-circle-o:before {
  content: "";
}

.fa-shopping-bag:before {
  content: "";
}

.fa-shopping-basket:before {
  content: "";
}

.fa-hashtag:before {
  content: "";
}

.fa-bluetooth:before {
  content: "";
}

.fa-bluetooth-b:before {
  content: "";
}

.fa-percent:before {
  content: "";
}

.fa-gitlab:before {
  content: "";
}

.fa-wpbeginner:before {
  content: "";
}

.fa-wpforms:before {
  content: "";
}

.fa-envira:before {
  content: "";
}

.fa-universal-access:before {
  content: "";
}

.fa-wheelchair-alt:before {
  content: "";
}

.fa-question-circle-o:before {
  content: "";
}

.fa-blind:before {
  content: "";
}

.fa-audio-description:before {
  content: "";
}

.fa-volume-control-phone:before {
  content: "";
}

.fa-braille:before {
  content: "";
}

.fa-assistive-listening-systems:before {
  content: "";
}

.fa-asl-interpreting:before,
.fa-american-sign-language-interpreting:before {
  content: "";
}

.fa-deafness:before,
.fa-hard-of-hearing:before,
.fa-deaf:before {
  content: "";
}

.fa-glide:before {
  content: "";
}

.fa-glide-g:before {
  content: "";
}

.fa-signing:before,
.fa-sign-language:before {
  content: "";
}

.fa-low-vision:before {
  content: "";
}

.fa-viadeo:before {
  content: "";
}

.fa-viadeo-square:before {
  content: "";
}

.fa-snapchat:before {
  content: "";
}

.fa-snapchat-ghost:before {
  content: "";
}

.fa-snapchat-square:before {
  content: "";
}

.fa-pied-piper:before {
  content: "";
}

.fa-first-order:before {
  content: "";
}

.fa-yoast:before {
  content: "";
}

.fa-themeisle:before {
  content: "";
}

.fa-google-plus-circle:before,
.fa-google-plus-official:before {
  content: "";
}

.fa-fa:before,
.fa-font-awesome:before {
  content: "";
}

.fa-handshake-o:before {
  content: "";
}

.fa-envelope-open:before {
  content: "";
}

.fa-envelope-open-o:before {
  content: "";
}

.fa-linode:before {
  content: "";
}

.fa-address-book:before {
  content: "";
}

.fa-address-book-o:before {
  content: "";
}

.fa-vcard:before,
.fa-address-card:before {
  content: "";
}

.fa-vcard-o:before,
.fa-address-card-o:before {
  content: "";
}

.fa-user-circle:before {
  content: "";
}

.fa-user-circle-o:before {
  content: "";
}

.fa-user-o:before {
  content: "";
}

.fa-id-badge:before {
  content: "";
}

.fa-drivers-license:before,
.fa-id-card:before {
  content: "";
}

.fa-drivers-license-o:before,
.fa-id-card-o:before {
  content: "";
}

.fa-quora:before {
  content: "";
}

.fa-free-code-camp:before {
  content: "";
}

.fa-telegram:before {
  content: "";
}

.fa-thermometer-4:before,
.fa-thermometer:before,
.fa-thermometer-full:before {
  content: "";
}

.fa-thermometer-3:before,
.fa-thermometer-three-quarters:before {
  content: "";
}

.fa-thermometer-2:before,
.fa-thermometer-half:before {
  content: "";
}

.fa-thermometer-1:before,
.fa-thermometer-quarter:before {
  content: "";
}

.fa-thermometer-0:before,
.fa-thermometer-empty:before {
  content: "";
}

.fa-shower:before {
  content: "";
}

.fa-bathtub:before,
.fa-s15:before,
.fa-bath:before {
  content: "";
}

.fa-podcast:before {
  content: "";
}

.fa-window-maximize:before {
  content: "";
}

.fa-window-minimize:before {
  content: "";
}

.fa-window-restore:before {
  content: "";
}

.fa-times-rectangle:before,
.fa-window-close:before {
  content: "";
}

.fa-times-rectangle-o:before,
.fa-window-close-o:before {
  content: "";
}

.fa-bandcamp:before {
  content: "";
}

.fa-grav:before {
  content: "";
}

.fa-etsy:before {
  content: "";
}

.fa-imdb:before {
  content: "";
}

.fa-ravelry:before {
  content: "";
}

.fa-eercast:before {
  content: "";
}

.fa-microchip:before {
  content: "";
}

.fa-snowflake-o:before {
  content: "";
}

.fa-superpowers:before {
  content: "";
}

.fa-wpexplorer:before {
  content: "";
}

.fa-meetup:before {
  content: "";
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/* montserrat-200 - latin */
@font-face {
  font-family: 'Montserrat-200';
  font-style: normal;
  font-weight: 200;
  src: url("fonts/montserrat/montserrat-v14-latin-200.eot");
  /* IE9 Compat Modes */
  src: local("Montserrat ExtraLight"), local("Montserrat-ExtraLight"), url("fonts/montserrat/montserrat-v14-latin-200.eot?#iefix") format("embedded-opentype"), url("fonts/montserrat/montserrat-v14-latin-200.woff2") format("woff2"), url("fonts/montserrat/montserrat-v14-latin-200.woff") format("woff"), url("fonts/montserrat/montserrat-v14-latin-200.ttf") format("truetype"), url("fonts/montserrat/montserrat-v14-latin-200.svg#Montserrat") format("svg");
  /* Legacy iOS */
}

/* montserrat-300 - latin */
@font-face {
  font-family: 'Montserrat-300';
  font-style: normal;
  font-weight: 300;
  src: url("fonts/montserrat/montserrat-v14-latin-300.eot");
  /* IE9 Compat Modes */
  src: local("Montserrat Light"), local("Montserrat-Light"), url("fonts/montserrat/montserrat-v14-latin-300.eot?#iefix") format("embedded-opentype"), url("fonts/montserrat/montserrat-v14-latin-300.woff2") format("woff2"), url("fonts/montserrat/montserrat-v14-latin-300.woff") format("woff"), url("fonts/montserrat/montserrat-v14-latin-300.ttf") format("truetype"), url("fonts/montserrat/montserrat-v14-latin-300.svg#Montserrat") format("svg");
  /* Legacy iOS */
}

/* montserrat-regular - latin */
@font-face {
  font-family: 'Montserrat-400';
  font-style: normal;
  font-weight: 400;
  src: url("fonts/montserrat/montserrat-v14-latin-regular.eot");
  /* IE9 Compat Modes */
  src: local("Montserrat Regular"), local("Montserrat-Regular"), url("fonts/montserrat/montserrat-v14-latin-regular.eot?#iefix") format("embedded-opentype"), url("fonts/montserrat/montserrat-v14-latin-regular.woff2") format("woff2"), url("fonts/montserrat/montserrat-v14-latin-regular.woff") format("woff"), url("fonts/montserrat/montserrat-v14-latin-regular.ttf") format("truetype"), url("fonts/montserrat/montserrat-v14-latin-regular.svg#Montserrat") format("svg");
  /* Legacy iOS */
}

/* montserrat-500 - latin */
@font-face {
  font-family: 'Montserrat-500';
  font-style: normal;
  font-weight: 500;
  src: url("fonts/montserrat/montserrat-v14-latin-500.eot");
  /* IE9 Compat Modes */
  src: local("Montserrat Medium"), local("Montserrat-Medium"), url("fonts/montserrat/montserrat-v14-latin-500.eot?#iefix") format("embedded-opentype"), url("fonts/montserrat/montserrat-v14-latin-500.woff2") format("woff2"), url("fonts/montserrat/montserrat-v14-latin-500.woff") format("woff"), url("fonts/montserrat/montserrat-v14-latin-500.ttf") format("truetype"), url("fonts/montserrat/montserrat-v14-latin-500.svg#Montserrat") format("svg");
  /* Legacy iOS */
}

/* montserrat-600 - latin */
@font-face {
  font-family: 'Montserrat-600';
  font-style: normal;
  font-weight: 600;
  src: url("fonts/montserrat/montserrat-v14-latin-600.eot");
  /* IE9 Compat Modes */
  src: local("Montserrat SemiBold"), local("Montserrat-SemiBold"), url("fonts/montserrat/montserrat-v14-latin-600.eot?#iefix") format("embedded-opentype"), url("fonts/montserrat/montserrat-v14-latin-600.woff2") format("woff2"), url("fonts/montserrat/montserrat-v14-latin-600.woff") format("woff"), url("fonts/montserrat/montserrat-v14-latin-600.ttf") format("truetype"), url("fonts/montserrat/montserrat-v14-latin-600.svg#Montserrat") format("svg");
  /* Legacy iOS */
}

/* montserrat-700 - latin */
@font-face {
  font-family: 'Montserrat-700';
  font-style: normal;
  font-weight: 700;
  src: url("fonts/montserrat/montserrat-v14-latin-700.eot");
  /* IE9 Compat Modes */
  src: local("Montserrat Bold"), local("Montserrat-Bold"), url("fonts/montserrat/montserrat-v14-latin-700.eot?#iefix") format("embedded-opentype"), url("fonts/montserrat/montserrat-v14-latin-700.woff2") format("woff2"), url("fonts/montserrat/montserrat-v14-latin-700.woff") format("woff"), url("fonts/montserrat/montserrat-v14-latin-700.ttf") format("truetype"), url("fonts/montserrat/montserrat-v14-latin-700.svg#Montserrat") format("svg");
  /* Legacy iOS */
}

/* montserrat-800 - latin */
@font-face {
  font-family: 'Montserrat-800';
  font-style: normal;
  font-weight: 800;
  src: url("fonts/montserrat/montserrat-v14-latin-800.eot");
  /* IE9 Compat Modes */
  src: local("Montserrat ExtraBold"), local("Montserrat-ExtraBold"), url("fonts/montserrat/montserrat-v14-latin-800.eot?#iefix") format("embedded-opentype"), url("fonts/montserrat/montserrat-v14-latin-800.woff2") format("woff2"), url("fonts/montserrat/montserrat-v14-latin-800.woff") format("woff"), url("fonts/montserrat/montserrat-v14-latin-800.ttf") format("truetype"), url("fonts/montserrat/montserrat-v14-latin-800.svg#Montserrat") format("svg");
  /* Legacy iOS */
}

/* montserrat-900 - latin */
@font-face {
  font-family: 'Montserrat-900';
  font-style: normal;
  font-weight: 900;
  src: url("fonts/montserrat/montserrat-v14-latin-900.eot");
  /* IE9 Compat Modes */
  src: local("Montserrat Black"), local("Montserrat-Black"), url("fonts/montserrat/montserrat-v14-latin-900.eot?#iefix") format("embedded-opentype"), url("fonts/montserrat/montserrat-v14-latin-900.woff2") format("woff2"), url("fonts/montserrat/montserrat-v14-latin-900.woff") format("woff"), url("fonts/montserrat/montserrat-v14-latin-900.ttf") format("truetype"), url("fonts/montserrat/montserrat-v14-latin-900.svg#Montserrat") format("svg");
  /* Legacy iOS */
}

/* josefin-sans-300 - latin */
@font-face {
  font-family: 'JosefinSans-300';
  font-style: normal;
  src: url("fonts/josefin/josefin-sans-v14-latin-300.eot");
  /* IE9 Compat Modes */
  src: local("Josefin Sans Light"), local("JosefinSans-Light"), url("fonts/josefin/josefin-sans-v14-latin-300.eot?#iefix") format("embedded-opentype"), url("fonts/josefin/josefin-sans-v14-latin-300.woff2") format("woff2"), url("fonts/josefin/josefin-sans-v14-latin-300.woff") format("woff"), url("fonts/josefin/josefin-sans-v14-latin-300.ttf") format("truetype"), url("fonts/josefin/josefin-sans-v14-latin-300.svg#JosefinSans") format("svg");
  /* Legacy iOS */
}

/* josefin-sans-regular - latin */
/* josefin-sans-100 - latin */
@font-face {
  font-family: 'JosefinSans-100';
  font-style: normal;
  src: url("fonts/josefin/josefin-sans-v14-latin-100.eot");
  /* IE9 Compat Modes */
  src: local("Josefin Sans Thin"), local("JosefinSans-Thin"), url("fonts/josefin/josefin-sans-v14-latin-100.eot?#iefix") format("embedded-opentype"), url("fonts/josefin/josefin-sans-v14-latin-100.woff2") format("woff2"), url("fonts/josefin/josefin-sans-v14-latin-100.woff") format("woff"), url("fonts/josefin/josefin-sans-v14-latin-100.ttf") format("truetype"), url("fonts/josefin/josefin-sans-v14-latin-100.svg#JosefinSans") format("svg");
  /* Legacy iOS */
}

@font-face {
  font-family: 'JosefinSans-400';
  font-style: normal;
  src: url("fonts/josefin/josefin-sans-v14-latin-regular.eot");
  /* IE9 Compat Modes */
  src: local("Josefin Sans Regular"), local("JosefinSans-Regular"), url("fonts/josefin/josefin-sans-v14-latin-regular.eot?#iefix") format("embedded-opentype"), url("fonts/josefin/josefin-sans-v14-latin-regular.woff2") format("woff2"), url("fonts/josefin/josefin-sans-v14-latin-regular.woff") format("woff"), url("fonts/josefin/josefin-sans-v14-latin-regular.ttf") format("truetype"), url("fonts/josefin/josefin-sans-v14-latin-regular.svg#JosefinSans") format("svg");
  /* Legacy iOS */
}

/* josefin-sans-600 - latin */
@font-face {
  font-family: 'JosefinSans-600';
  font-style: normal;
  font-weight: 600;
  src: url("fonts/josefin/josefin-sans-v14-latin-600.eot");
  /* IE9 Compat Modes */
  src: local("Josefin Sans SemiBold"), local("JosefinSans-SemiBold"), url("fonts/josefin/josefin-sans-v14-latin-600.eot?#iefix") format("embedded-opentype"), url("fonts/josefin/josefin-sans-v14-latin-600.woff2") format("woff2"), url("fonts/josefin/josefin-sans-v14-latin-600.woff") format("woff"), url("fonts/josefin/josefin-sans-v14-latin-600.ttf") format("truetype"), url("fonts/josefin/josefin-sans-v14-latin-600.svg#JosefinSans") format("svg");
  /* Legacy iOS */
}

/* josefin-sans-700 - latin */
@font-face {
  font-family: 'JosefinSans-700';
  font-style: normal;
  font-weight: 700;
  src: url("fonts/josefin/josefin-sans-v14-latin-700.eot");
  /* IE9 Compat Modes */
  src: local("Josefin Sans Bold"), local("JosefinSans-Bold"), url("fonts/josefin/josefin-sans-v14-latin-700.eot?#iefix") format("embedded-opentype"), url("fonts/josefin/josefin-sans-v14-latin-700.woff2") format("woff2"), url("fonts/josefin/josefin-sans-v14-latin-700.woff") format("woff"), url("fonts/josefin/josefin-sans-v14-latin-700.ttf") format("truetype"), url("fonts/josefin/josefin-sans-v14-latin-700.svg#JosefinSans") format("svg");
  /* Legacy iOS */
}

/* acme-regular - latin */
@font-face {
  font-family: 'Acme';
  font-style: normal;
  font-weight: 400;
  src: url("fonts/acme/acme-v9-latin-regular.eot");
  /* IE9 Compat Modes */
  src: local("Acme Regular"), local("Acme-Regular"), url("fonts/acme/acme-v9-latin-regular.eot?#iefix") format("embedded-opentype"), url("fonts/acme/acme-v9-latin-regular.woff2") format("woff2"), url("fonts/acme/acme-v9-latin-regular.woff") format("woff"), url("fonts/acme/acme-v9-latin-regular.ttf") format("truetype"), url("fonts/acme/acme-v9-latin-regular.svg#Acme") format("svg");
  /* Legacy iOS */
}

/*--------------------------------------------------------------
3.0 Variables
--------------------------------------------------------------*/
/*--------------------------------------------------------------
4.0 Mixins
--------------------------------------------------------------*/
/*--------------------------------------------------------------
5.0 Bootstrap Grid
--------------------------------------------------------------*/
/*!
 * Bootstrap Grid v4.0.0 (https://getbootstrap.com)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
@-ms-viewport {
  width: device-width;
}

html {
  box-sizing: border-box;
  -ms-overflow-style: scrollbar;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

.container,
.extra-container,
.lightbox-container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container,
  .extra-container,
  .lightbox-container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container,
  .extra-container,
  .lightbox-container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container,
  .extra-container,
  .lightbox-container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container,
  .extra-container,
  .lightbox-container {
    max-width: 1200px;
  }
}

@media (min-width: 1752px) {
  .extra-container {
    max-width: 1752px;
  }
  .lightbox-container {
    max-width: 1340px;
    padding: 0 85px;
  }
}

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*='col-'] {
  padding-right: 0;
  padding-left: 0;
}

.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col,
.col-auto,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm,
.col-sm-auto,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md,
.col-md-auto,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg,
.col-lg-auto,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl,
.col-xl-auto {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: none;
}

.col-1 {
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}

.col-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.col-5 {
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.col-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.col-11 {
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.order-first {
  order: -1;
}

.order-last {
  order: 13;
}

.order-0 {
  order: 0;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-5 {
  order: 5;
}

.order-6 {
  order: 6;
}

.order-7 {
  order: 7;
}

.order-8 {
  order: 8;
}

.order-9 {
  order: 9;
}

.order-10 {
  order: 10;
}

.order-11 {
  order: 11;
}

.order-12 {
  order: 12;
}

.offset-1 {
  margin-left: 8.333333%;
}

.offset-2 {
  margin-left: 16.666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.333333%;
}

.offset-5 {
  margin-left: 41.666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.333333%;
}

.offset-8 {
  margin-left: 66.666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.333333%;
}

.offset-11 {
  margin-left: 91.666667%;
}

@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-sm-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .col-sm-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-sm-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-sm-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .col-sm-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-sm-first {
    order: -1;
  }
  .order-sm-last {
    order: 13;
  }
  .order-sm-0 {
    order: 0;
  }
  .order-sm-1 {
    order: 1;
  }
  .order-sm-2 {
    order: 2;
  }
  .order-sm-3 {
    order: 3;
  }
  .order-sm-4 {
    order: 4;
  }
  .order-sm-5 {
    order: 5;
  }
  .order-sm-6 {
    order: 6;
  }
  .order-sm-7 {
    order: 7;
  }
  .order-sm-8 {
    order: 8;
  }
  .order-sm-9 {
    order: 9;
  }
  .order-sm-10 {
    order: 10;
  }
  .order-sm-11 {
    order: 11;
  }
  .order-sm-12 {
    order: 12;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.333333%;
  }
  .offset-sm-2 {
    margin-left: 16.666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.333333%;
  }
  .offset-sm-5 {
    margin-left: 41.666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.333333%;
  }
  .offset-sm-8 {
    margin-left: 66.666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.333333%;
  }
  .offset-sm-11 {
    margin-left: 91.666667%;
  }
}

@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-md-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .col-md-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-md-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-md-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-md-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .col-md-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-md-first {
    order: -1;
  }
  .order-md-last {
    order: 13;
  }
  .order-md-0 {
    order: 0;
  }
  .order-md-1 {
    order: 1;
  }
  .order-md-2 {
    order: 2;
  }
  .order-md-3 {
    order: 3;
  }
  .order-md-4 {
    order: 4;
  }
  .order-md-5 {
    order: 5;
  }
  .order-md-6 {
    order: 6;
  }
  .order-md-7 {
    order: 7;
  }
  .order-md-8 {
    order: 8;
  }
  .order-md-9 {
    order: 9;
  }
  .order-md-10 {
    order: 10;
  }
  .order-md-11 {
    order: 11;
  }
  .order-md-12 {
    order: 12;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.333333%;
  }
  .offset-md-2 {
    margin-left: 16.666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.333333%;
  }
  .offset-md-5 {
    margin-left: 41.666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.333333%;
  }
  .offset-md-8 {
    margin-left: 66.666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.333333%;
  }
  .offset-md-11 {
    margin-left: 91.666667%;
  }
}

@media (min-width: 992px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-lg-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .col-lg-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-lg-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-lg-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .col-lg-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-lg-first {
    order: -1;
  }
  .order-lg-last {
    order: 13;
  }
  .order-lg-0 {
    order: 0;
  }
  .order-lg-1 {
    order: 1;
  }
  .order-lg-2 {
    order: 2;
  }
  .order-lg-3 {
    order: 3;
  }
  .order-lg-4 {
    order: 4;
  }
  .order-lg-5 {
    order: 5;
  }
  .order-lg-6 {
    order: 6;
  }
  .order-lg-7 {
    order: 7;
  }
  .order-lg-8 {
    order: 8;
  }
  .order-lg-9 {
    order: 9;
  }
  .order-lg-10 {
    order: 10;
  }
  .order-lg-11 {
    order: 11;
  }
  .order-lg-12 {
    order: 12;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.333333%;
  }
  .offset-lg-2 {
    margin-left: 16.666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.333333%;
  }
  .offset-lg-5 {
    margin-left: 41.666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.333333%;
  }
  .offset-lg-8 {
    margin-left: 66.666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.333333%;
  }
  .offset-lg-11 {
    margin-left: 91.666667%;
  }
}

@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-xl-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .col-xl-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-xl-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-xl-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .col-xl-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xl-first {
    order: -1;
  }
  .order-xl-last {
    order: 13;
  }
  .order-xl-0 {
    order: 0;
  }
  .order-xl-1 {
    order: 1;
  }
  .order-xl-2 {
    order: 2;
  }
  .order-xl-3 {
    order: 3;
  }
  .order-xl-4 {
    order: 4;
  }
  .order-xl-5 {
    order: 5;
  }
  .order-xl-6 {
    order: 6;
  }
  .order-xl-7 {
    order: 7;
  }
  .order-xl-8 {
    order: 8;
  }
  .order-xl-9 {
    order: 9;
  }
  .order-xl-10 {
    order: 10;
  }
  .order-xl-11 {
    order: 11;
  }
  .order-xl-12 {
    order: 12;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.333333%;
  }
  .offset-xl-2 {
    margin-left: 16.666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.333333%;
  }
  .offset-xl-5 {
    margin-left: 41.666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.333333%;
  }
  .offset-xl-8 {
    margin-left: 66.666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.333333%;
  }
  .offset-xl-11 {
    margin-left: 91.666667%;
  }
}

.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
}

@media print {
  .d-print-none {
    display: none !important;
  }
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

@media (min-width: 576px) {
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 768px) {
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 992px) {
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 1200px) {
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
}

/*--------------------------------------------------------------
6.0 Accessibility
--------------------------------------------------------------*/
/* Text meant only for screen readers. */
.screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  overflow: hidden;
  position: absolute !important;
  width: 1px;
  word-wrap: normal !important;
  /* Many screen reader and browser combinations announce broken words as they would appear visually. */
}

.screen-reader-text:focus {
  background-color: #f1f1f1;
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  color: #21759b;
  display: block;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: 700;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
  /* Above WP toolbar. */
}

/*--------------------------------------------------------------
7.0 Alignments
--------------------------------------------------------------*/
.alignleft {
  display: inline;
  float: left;
  margin-right: 1.5em;
}

.alignright {
  display: inline;
  float: right;
  margin-left: 1.5em;
}

.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/*--------------------------------------------------------------
8.0 Typography
--------------------------------------------------------------*/
html {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body,
button,
input,
select,
textarea {
  font-family: "Montserrat-400", sans-serif;
  font-size: 15px;
  font-size: 0.9375rem;
  font-weight: 400;
  line-height: 1.6;
}

p {
  font-family: "Montserrat-400", sans-serif;
  font-size: 15px;
  color: #666666;
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #000000;
  margin: 0;
}

h1 {
  font-size: 80px;
  font-size: 5rem;
  font-family: "Montserrat-700", sans-serif;
}

h2 {
  font-size: 40px;
  font-size: 2.5rem;
  font-family: "Montserrat-700", sans-serif;
}

h3 {
  font-size: 30px;
  font-size: 1.875rem;
  font-family: "Montserrat-600", sans-serif;
}

h4 {
  font-size: 35px;
  font-size: 2.1875rem;
  font-family: "JosefinSans-600", sans-serif;
}

h5 {
  font-size: 22px;
  font-size: 1.375rem;
  font-family: "JosefinSans-600", sans-serif;
}

h6 {
  font-size: 15px;
  font-size: 0.9375rem;
  font-family: "JosefinSans-400", sans-serif;
}

/*--------------------------------------------------------------
9.0 Form
--------------------------------------------------------------*/
label {
  display: block;
  margin-bottom: 15px;
  color: #000000;
  font-size: 15px;
  font-size: 0.9375rem;
}

fieldset {
  margin-bottom: 1rem;
}

input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"],
textarea {
  display: block;
  width: 100%;
  padding: 10px 15px;
  border: unset;
  border-bottom: 1px solid #e6e6e6;
  background: #ffffff;
  border-radius: 3px;
  box-shadow: none;
  transition: all 0.3s ease-out;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="range"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
input[type="time"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="color"]:focus,
textarea:focus {
  outline: 0;
  border-bottom: 1px solid #000000;
}

input[type="submit"] {
  transition: all 0.3s;
  border: none;
  background-color: transparent;
  font-family: "JosefinSans-600", sans-serif;
  letter-spacing: -1px;
  text-transform: uppercase;
  border-bottom: 1px solid #666666;
  padding: 0;
  height: 18px;
}

textarea {
  overflow: auto;
}

/* Placeholder text color -- selectors need to be separate to work. */
::-webkit-input-placeholder,
:-moz-placeholder,
:-ms-input-placeholder {
  color: #999999;
  font-family: "Montserrat-400", sans-serif;
}

/*--------------------------------------------------------------
10.0 Tables
--------------------------------------------------------------*/
table {
  border-collapse: collapse;
  margin: 0 0 1.5rem;
  /* Prevents HTML tables from becoming too wide */
  width: 100%;
}

thead th {
  border-bottom: 2px solid #e6e6e6;
  padding-bottom: 0.5rem;
}

th {
  padding: 0.4rem;
  text-align: left;
}

tr {
  border-bottom: 1px solid #e6e6e6;
}

td {
  padding: 0.4rem;
}

th:first-child,
td:first-child {
  padding-left: 0;
}

th:last-child,
td:last-child {
  padding-right: 0;
}

/*--------------------------------------------------------------
11.0 Formatting
--------------------------------------------------------------*/
hr {
  height: 1px;
  margin-bottom: 1.5rem;
  border: 0;
  background-color: #e6e6e6;
}

img {
  max-width: 100%;
  vertical-align: middle;
  transition: 0.3s all ease-out;
}

.content {
  padding: 0 95px;
}

.moving-btn {
  position: relative;
}

.loader-spin {
  width: 45px;
  height: 45px;
  display: inline-block;
  padding: 0px;
  border-radius: 100%;
  border-top: 5px solid #000000;
  border-bottom: 5px solid rgba(153, 153, 153, 0.6);
  border-left: 5px solid #000000;
  border-right: 5px solid rgba(153, 153, 153, 0.6);
  -webkit-animation: loaderspin  1s ease-in-out infinite;
  animation: loaderspin 1s ease-in-out infinite;
}

@keyframes loaderspin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes loaderspin {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
12.0 Lists
--------------------------------------------------------------*/
ul,
ol {
  margin: 0;
  padding: 0;
}

ul {
  list-style: disc;
  margin: 0;
}

ol {
  list-style: decimal;
}

li > ul,
li > ol {
  margin-bottom: 0;
  margin-left: 1.5rem;
}

dt {
  font-weight: 700;
}

dd {
  margin: 0 1.5rem 1.5rem;
}

.min-list {
  margin: 0;
  padding: 0;
}

.min-list li {
  list-style: none;
}

.inline-list li {
  display: inline-block;
}

/*--------------------------------------------------------------
13.0 Links
--------------------------------------------------------------*/
a {
  color: inherit;
  text-decoration: none;
  transition: 0.3s all ease-out;
  cursor: none;
}

a:hover, a:focus, a:active {
  color: #000000;
}

a:focus {
  outline: thin dotted;
}

a:hover, a:active {
  outline: none;
}

/*--------------------------------------------------------------
14.0 Navigation
--------------------------------------------------------------*/
/*--------------------------------------------------------------
15.0 Comments
--------------------------------------------------------------*/
/*--------------------------------------------------------------
16.0 Widgets
--------------------------------------------------------------*/
/*--------------------------------------------------------------
17.0 Utilities
--------------------------------------------------------------*/
.t-center {
  text-align: center;
}

.t-left {
  text-align: left;
}

.t-right {
  text-align: right;
}

.t-white {
  color: #ffffff;
}

.t-dove {
  color: #666666;
}

.t-dusty {
  color: #999999;
}

.t-main-dark {
  color: #000000;
}

.t-nd-dark {
  color: #333333;
}

/*--------------------------------------------------------------
GENERAL
--------------------------------------------------------------*/
.cursor {
  pointer-events: none;
  position: fixed;
  width: 5px;
  height: 5px;
  background-color: #000000;
  border-radius: 50%;
  mix-blend-mode: difference;
  z-index: 999;
}

.cursor-follower {
  pointer-events: none;
  position: fixed;
  height: 30px;
  width: 30px;
  border: 2px solid #000000;
  border-radius: 50%;
  z-index: 999;
}

.cursor-white {
  background-color: #ffffff;
}

.cursor-folow-white {
  border: 2px solid #ffffff;
}

.cursor-folow-transform {
  border: unset;
  background-color: rgba(153, 153, 153, 0.3);
}

.cursor-transform {
  background-color: transparent;
}

.pagebanner-1 {
  height: 810px;
  width: 100%;
  background-image: url("../images/pagebannerBrg.jpg");
  background-position: center;
  background-size: cover;
  display: flex;
  align-items: center;
}

.pagebanner-1__tittle {
  margin-left: 130px;
}

.pagebanner-1__tittle__conference {
  font-family: "JosefinSans-700", sans-serif;
  font-size: 95px;
  color: #ffffff;
  margin: 0;
}

.pagebanner-1__tittle__webdesign {
  font-family: "JosefinSans-100", sans-serif;
  font-size: 95px;
  color: #ffffff;
  margin: 0;
  line-height: 100px;
}

.pagebanner-1__tittle .letter {
  opacity: 0;
}

@media (max-width: 1752px) {
  .pagebanner-1__tittle {
    margin-left: 0;
  }
}

@media screen and (max-width: 1199px) {
  .pagebanner-1 {
    height: 600px;
  }
  .pagebanner-1__tittle__conference {
    font-family: "JosefinSans-700", sans-serif;
    font-size: 75px;
    color: #ffffff;
    margin: 0;
  }
  .pagebanner-1__tittle__webdesign {
    font-family: "JosefinSans-100", sans-serif;
    font-size: 75px;
    color: #ffffff;
    margin: 0;
    line-height: 75px;
  }
}

@media (max-width: 992px) {
  .pagebanner-1 {
    height: 400px;
  }
  .pagebanner-1__tittle__conference {
    font-family: "JosefinSans-700", sans-serif;
    font-size: 55px;
    color: #ffffff;
    margin: 0;
  }
  .pagebanner-1__tittle__webdesign {
    font-family: "JosefinSans-100", sans-serif;
    font-size: 55px;
    color: #ffffff;
    margin: 0;
  }
}

@media screen and (max-width: 767px) {
  .pagebanner-1 {
    height: 300px;
  }
  .pagebanner-1__tittle {
    width: 65% !important;
  }
  .pagebanner-1__tittle__conference {
    font-family: "JosefinSans-700", sans-serif;
    font-size: 45px;
    color: #ffffff;
    margin: 0;
    line-height: 45px;
  }
  .pagebanner-1__tittle__webdesign {
    font-family: "JosefinSans-100", sans-serif;
    font-size: 45px;
    color: #ffffff;
    margin: 0;
  }
}

@media screen and (max-width: 576px) {
  .pagebanner-1 {
    height: 250px;
  }
  .pagebanner-1__tittle__conference {
    font-family: "JosefinSans-700", sans-serif;
    font-size: 35px;
    color: #ffffff;
    margin: 0;
  }
  .pagebanner-1__tittle__webdesign {
    font-family: "JosefinSans-100", sans-serif;
    font-size: 35px;
    color: #ffffff;
    margin: 0;
  }
}

.blog-area {
  padding: 130px 0 128px;
}

.blog__item {
  visibility: hidden;
  width: 370px;
  margin-bottom: 68px;
}

.blog__item > a {
  display: block;
}

.blog__item:hover .blog__item__thumb img {
  /* IE 9 */
  /* Safari 3-8 */
  transform: scale(1.1);
}

.blog__item__thumb {
  overflow: hidden;
}

.blog__item__info span {
  display: inline-block;
  color: #999999;
  margin: 18px 28px 20px 0;
}

.blog__item__info span i {
  margin-right: 5px;
}

.blog__load-more {
  text-align: center;
}

.blog__load-more a {
  position: relative;
  color: #000000;
  text-transform: uppercase;
  font-family: "JosefinSans-600", sans-serif;
  display: inline-block;
}

.blog__load-more a::before {
  bottom: 3px;
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  border-bottom: 1px solid #a3a3a3;
  border-top: 1px solid #c8c8c8;
}

.blog__load-more .blog__load-more__btn {
  display: inline-block;
  position: relative;
}

@media (max-width: 991px) {
  .blog__load-more .blog__load-more__btn svg {
    display: none;
  }
}

.blog__load-more .blog__load-more__btn:hover path {
  stroke: #000000;
  -webkit-animation: draw 1s linear alternate forwards;
  animation: draw 1s linear alternate forwards;
}

.blog__load-more .blog__load-more__btn svg {
  position: absolute;
  width: 150px;
  top: -50px;
  left: -30px;
}

.blog__load-more .blog__load-more__btn svg path {
  -webkit-animation: drawReverse 0.5s;
  animation: drawReverse 0.5s;
  fill: none;
  stroke: transparent;
  transition: stroke 1s;
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
}

@keyframes draw {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@-webkit-keyframes draw {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes drawReverse {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 1000;
  }
}

@-webkit-keyframes drawReverse {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 1000;
  }
}

@media screen and (max-width: 1199px) {
  .blog-area {
    padding: 60px 0 60px;
  }
  .blog__item {
    width: 31.2%;
    margin-bottom: 40px;
  }
  .blog__item__info span {
    margin: 10px 15px 15px 0;
  }
}

@media screen and (max-width: 990px) {
  .blog__item {
    width: 47.8%;
    margin-bottom: 40px;
  }
}

@media (max-width: 767px) {
  .blog-area {
    padding: 40px 0 40px;
  }
  .blog__item {
    width: 70%;
    left: 50% !important;
    transform: translate(-50%, 0);
  }
  .blog__item__info span {
    margin: 10px 15px 10px 0;
    font-size: 13px;
  }
  .blog__item__info h5 {
    font-size: 18px;
  }
  .blog__item__info p {
    font-size: 14px;
  }
}

@media (max-width: 370px) {
  .blog__item {
    width: 100%;
  }
}

.blog-single-content {
  padding: 130px 100px;
}

.blog-single-content h4 {
  margin-bottom: 15px;
}

.blog-single-content__thumb {
  height: 650px;
}

.blog-single-content__thumb img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.blog-single-content__time {
  margin: 17px 0 40px;
}

.blog-single-content__time span {
  font-size: 18px;
  margin-right: 35px;
  color: #999999;
}

.blog-single-content__time span i {
  margin-right: 8px;
}

.blog-single-content__paragraph p {
  margin-top: 15px;
}

.blog-single-content__paragraph p:first-child {
  margin-top: 0;
}

.blog-single-content__tags {
  display: flex;
  align-items: center;
}

.blog-single-content__tags span {
  font-family: "Montserrat-400", sans-serif;
  font-size: 15px;
  color: #666666;
  margin: 0 3px;
}

.blog-single-content__tags span:first-child {
  font-family: "JosefinSans-600", sans-serif;
  font-size: 18px;
  color: #000000;
  margin: 0 8px 0 0;
}

.blog-single-content__quote {
  margin: 65px 0 50px;
}

.blog-single-content__quote__text {
  padding: 30px 0 30px 30px;
}

.blog-single-content__quote__text p {
  position: inherit;
}

.blog-single-content__quote__main {
  margin-bottom: 43px;
  padding: 43px 40px;
  background-color: #f2f2f2;
  border-left: 6px solid #000000;
}

.blog-single-content__quote__main p {
  color: #333333;
  font-size: 18px;
}

.blog-single-content__paging {
  display: flex;
  height: 150px;
  border-top: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
  margin: 55px 0;
}

.blog-single-content__paging__divide {
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
}

.blog-single-content__paging__divide--next {
  text-align: right;
  flex-direction: row-reverse;
  position: relative;
}

.blog-single-content__paging__divide--next::before {
  position: absolute;
  content: "";
  height: 70%;
  width: 0px;
  left: 0;
  border-left: 1px solid #e6e6e6;
}

.blog-single-content__paging__btn span {
  font-family: "JosefinSans-600", sans-serif;
  font-size: 18px;
  color: #666666;
  margin: 0 0 10px 0;
  display: inline-block;
}

.blog-single-content__paging__btn span .fa-angle-double-left {
  margin-right: 10px;
}

.blog-single-content__paging__btn span .fa-angle-double-right {
  margin-left: 10px;
}

.blog-single-content__paging__btn span, .blog-single-content__paging__btn h5 {
  transition: all 0.3s;
  color: #999999;
}

.blog-single-content__paging__btn:hover span, .blog-single-content__paging__btn:hover h5 {
  color: #000000;
}

.blog-single-content__writer {
  border-bottom: 1px solid #e6e6e6;
  padding: 5px 0 60px;
  display: flex;
}

.blog-single-content__writer__avatar {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
}

.blog-single-content__writer__text {
  padding-left: 30px;
}

.blog-single-content__writer__text h3 {
  display: inline-block;
  font-size: 15px;
  margin-right: 10px;
}

.blog-single-content__writer__text__role {
  font-family: "Montserrat-400", sans-serif;
  font-size: 14px;
  color: #999999;
  margin: 0 10px;
}

.blog-single-content__writer__text p {
  margin: 10px 0 15px;
}

.blog-single-content__writer__text a:hover i {
  color: #000000 !important;
}

.blog-single-content__writer__text i {
  font-size: 14px;
  color: #999999;
  margin-right: 15px;
}

@media screen and (max-width: 1199px) {
  .blog-single-content {
    padding: 60px 0;
  }
  .blog-single-content__thumb {
    height: 450px;
  }
  .blog-single-content__time {
    margin: 17px 0;
  }
  .blog-single-content h4 {
    font-size: 30px;
  }
  .blog-single-content__quote {
    margin: 30px 0;
  }
  .blog-single-content .blog-single-content__quote__text {
    padding: 0;
  }
  .blog-single-content .blog-single-content__quote__main {
    padding: 30px;
    margin-bottom: 30px;
  }
}

@media (max-width: 992px) {
  .blog-single-content__paging {
    margin: 40px 0;
  }
  .blog-single-content__writer {
    padding: 0 0 40px 0;
  }
}

@media screen and (max-width: 767px) {
  .blog-single-content__quote__main {
    margin-top: 30px;
    padding: 20px !important;
  }
  .blog-single-content__quote__main p {
    font-size: 16px;
  }
  .blog-single-content__paging h5 {
    font-size: 18px;
  }
}

@media (max-width: 576px) {
  .blog-single-content h4 {
    font-size: 22px;
  }
  .blog-single-content__time span {
    font-size: 15px;
    margin-right: 20px;
  }
  .blog-single-content__thumb {
    height: 320px;
  }
  .blog-single-content__writer__avatar {
    width: 80px;
    height: 80px;
  }
  .comment__item__text, .blog-single-content__writer__text {
    padding-left: 15px !important;
  }
  .blog-single-content__paging h5 {
    font-size: 16px;
  }
  .comment__item__text span {
    display: block;
    margin: 0 !important;
  }
}

.comment__item {
  display: flex;
  margin-top: 20px;
}

.comment__item__thumb {
  width: 70px;
  height: 70px;
  flex-shrink: 0;
}

.comment__item__text {
  padding-left: 30px;
}

.comment__item__text__rep {
  font-size: 13px;
  color: #000000 !important;
}

.comment__item__text__rep .fa-reply {
  margin-right: 5px;
}

.comment__item h3 {
  display: inline-block;
  font-size: 15px;
}

.comment__item span {
  font-family: "Montserrat-400", sans-serif;
  font-size: 14px;
  color: #999999;
  margin: 0 0 0 25px;
}

.comment__item p {
  margin: 10px 0 15px;
}

.comment {
  padding: 60px 0 0;
}

.comment h5 {
  font-size: 25px;
  margin-bottom: 30px;
}

.comment__leave-comment {
  margin-top: 50px;
}

@media (max-width: 768px) {
  .comment {
    padding-top: 40px;
  }
  .comment h5 {
    margin-bottom: 15px;
  }
  .comment__leave-comment {
    margin-top: 30px;
  }
}

@media (max-width: 576px) {
  .comment h5 {
    font-size: 20px;
  }
}

.fillin-form textarea {
  height: 180px;
  margin-bottom: 47px;
}

.fillin-form__fields {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
}

.fillin-form__fields input {
  width: calc(50% - 15px);
}

.fillin-form button {
  position: relative;
  color: #000000;
  text-transform: uppercase;
  font-family: "JosefinSans-600", sans-serif;
  display: inline-block;
  background-color: transparent;
  border: unset;
}

.fillin-form button::before {
  bottom: 3px;
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  border-bottom: 1px solid #a3a3a3;
  border-top: 1px solid #c8c8c8;
}

.fillin-form .fillin-form__submit {
  display: inline-block;
  position: relative;
}

@media (max-width: 991px) {
  .fillin-form .fillin-form__submit svg {
    display: none;
  }
}

.fillin-form .fillin-form__submit:hover path {
  stroke: #000000;
  -webkit-animation: draw 1s linear alternate forwards;
  animation: draw 1s linear alternate forwards;
}

.fillin-form .fillin-form__submit svg {
  position: absolute;
  width: 100px;
  top: -30px;
  left: -30px;
}

.fillin-form .fillin-form__submit svg path {
  -webkit-animation: drawReverse 0.5s;
  animation: drawReverse 0.5s;
  fill: none;
  stroke: transparent;
  transition: stroke 1s;
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
}

@keyframes draw {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@-webkit-keyframes draw {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes drawReverse {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 1000;
  }
}

@-webkit-keyframes drawReverse {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 1000;
  }
}

@media (max-width: 768px) {
  .fillin-form__fields {
    flex-direction: column;
  }
  .fillin-form__fields input {
    width: 100%;
    margin-bottom: 15px;
  }
  .fillin-form .fillin-form__fields {
    margin-bottom: 0;
  }
  .fillin-form textarea {
    margin-bottom: 30px !important;
  }
}

.contact__tittle h1 {
  letter-spacing: -4px;
  text-align: center;
  -webkit-text-stroke-width: 4px;
  -webkit-text-stroke-color: #000000;
  line-height: 130px;
  color: #ffffff;
  text-decoration: none;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(0deg, #000000, #000000 50%, #ffffff 50%);
  background-size: 100% 200%;
  background-position: 0% 0%;
  font-family: "Montserrat-700", sans-serif;
  font-size: 120px;
  color: #ffffff;
  margin: 155px 0 135px 0;
}

.contact__tittle h1:hover {
  transition: all 0.8s cubic-bezier(0, 0, 0.23, 1);
  background-position: 100% 100%;
}

@media (max-width: 1200px) {
  .contact__tittle h1 {
    line-height: 110px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 100px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 992px) {
  .contact__tittle h1 {
    -webkit-text-stroke-width: 2px;
    line-height: 80px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 70px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 768px) {
  .contact__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 65px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 55px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

@media (max-width: 576px) {
  .contact__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 45px !important;
    -webkit-text-stroke-width: 1px;
    font-family: "Montserrat-700", sans-serif;
    font-size: 35px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

.contact-info h4 {
  font-size: 40px;
  margin-bottom: 27px;
}

.contact-info__link {
  display: flex;
  align-items: center;
  padding: 14px 0;
  text-shadow: 0px 0.5px #adadad;
}

.contact-info__link img {
  flex-shrink: 0;
  margin-right: 30px;
}

.contact-info__item-left {
  padding-left: 70px;
}

@media screen and (max-width: 1199px) {
  .contact-info__item-left {
    padding-left: 0;
  }
}

@media (max-width: 992px) {
  .contact-info__item-left .fillin-form {
    margin-top: 40px;
  }
  .contact-info__item-left h4 {
    margin-bottom: 15px !important;
    line-height: 35px;
    font-size: 35px !important;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .contact-info__item-left h4 {
    font-size: 27px !important;
  }
}

.sponsor__tittle h1 {
  letter-spacing: -4px;
  text-align: center;
  -webkit-text-stroke-width: 4px;
  -webkit-text-stroke-color: #000000;
  line-height: 130px;
  color: #ffffff;
  text-decoration: none;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(0deg, #000000, #000000 50%, #ffffff 50%);
  background-size: 100% 200%;
  background-position: 0% 0%;
  font-family: "Montserrat-700", sans-serif;
  font-size: 120px;
  color: #ffffff;
  margin: 115px 0 105px 0;
}

.sponsor__tittle h1:hover {
  transition: all 0.8s cubic-bezier(0, 0, 0.23, 1);
  background-position: 100% 100%;
}

@media (max-width: 1200px) {
  .sponsor__tittle h1 {
    line-height: 110px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 100px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 992px) {
  .sponsor__tittle h1 {
    -webkit-text-stroke-width: 2px;
    line-height: 80px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 70px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 768px) {
  .sponsor__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 65px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 55px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

@media (max-width: 576px) {
  .sponsor__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 45px !important;
    -webkit-text-stroke-width: 1px;
    font-family: "Montserrat-700", sans-serif;
    font-size: 35px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

.sponsor {
  margin-bottom: 100px;
  height: 400px;
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

.sponsor::before {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  border: 2px solid #ffffff;
  z-index: 0;
}

.sponsor::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  border: 2px solid #ffffff;
  z-index: 0;
}

.sponsor__item {
  width: 20%;
  height: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-left: 1px solid #e6e6e6;
  border-top: 1px solid #e6e6e6;
}

.sponsor__item img {
  /* IE 9 */
  /* Safari 3-8 */
  transform: scale(0);
}

.sponsor__item:hover img {
  -moz-filter: hue-rotate(0deg) saturate(0%) brightness(0%);
  -webkit-filter: hue-rotate(0deg) saturate(0%) brightness(0%);
  filter: hue-rotate(0deg) saturate(0%) brightness(0%);
}

@media screen and (max-width: 1199px) {
  .sponsor {
    height: 340px;
  }
  .sponsor__item img {
    max-width: 80%;
  }
}

@media screen and (max-width: 990px) {
  .sponsor {
    height: 300px;
    margin-bottom: 60px;
  }
}

@media (max-width: 767px) {
  .sponsor {
    margin-bottom: 40px;
    height: auto;
  }
  .sponsor::after {
    border: 2px solid transparent;
  }
  .sponsor__item {
    height: 100px;
    border: unset;
    display: inline-flex !important;
    align-items: center;
  }
  .sponsor__item img {
    margin: auto;
  }
}

.schedule__tittle h1 {
  letter-spacing: -4px;
  text-align: center;
  -webkit-text-stroke-width: 4px;
  -webkit-text-stroke-color: #000000;
  line-height: 130px;
  color: #ffffff;
  text-decoration: none;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(0deg, #000000, #000000 50%, #ffffff 50%);
  background-size: 100% 200%;
  background-position: 0% 0%;
  font-family: "Montserrat-700", sans-serif;
  font-size: 120px;
  color: #ffffff;
  margin: 115px 0 115px 0;
}

.schedule__tittle h1:hover {
  transition: all 0.8s cubic-bezier(0, 0, 0.23, 1);
  background-position: 100% 100%;
}

@media (max-width: 1200px) {
  .schedule__tittle h1 {
    line-height: 110px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 100px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 992px) {
  .schedule__tittle h1 {
    -webkit-text-stroke-width: 2px;
    line-height: 80px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 70px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 768px) {
  .schedule__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 65px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 55px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

@media (max-width: 576px) {
  .schedule__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 45px !important;
    -webkit-text-stroke-width: 1px;
    font-family: "Montserrat-700", sans-serif;
    font-size: 35px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

.schedule-tab {
  height: 140px;
  display: flex;
}

.schedule-tab__item {
  width: 33.333%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border: 1px solid #e6e6e6;
  border-left: unset;
  position: relative;
}

.schedule-tab__item:first-child {
  border-left: 1px solid #e6e6e6;
}

.schedule-tab__item h2 {
  transition: all 0.3s ease;
  font-size: 35px;
}

.schedule-tab__item p {
  transition: all 0.3s ease;
  font-size: 22px;
  color: #000000;
}

.schedule-tab__item::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #000000;
  z-index: -1;
  /* IE 9 */
  /* Safari 3-8 */
  transform: scaleY(0);
  transform-origin: 50% 100%;
  transition-property: transform;
  transition: 300ms ease-out;
}

.schedule-tab__item:hover::before {
  /* IE 9 */
  /* Safari 3-8 */
  transform: scaleY(1);
  transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
}

.schedule-tab__item:hover h2, .schedule-tab__item:hover p {
  color: #ffffff;
}

.schedule-table {
  padding-top: 150px;
}

.schedule-table__greating {
  border-top: unset !important;
}

.schedule-table__end {
  height: 130px !important;
}

.schedule-table h3 {
  font-size: 20px;
  margin-bottom: 7px;
}

.schedule-table__time {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-right: 1px solid #e6e6e6;
  border-top: 1px solid #e6e6e6;
  height: 100%;
}

.schedule-table__time h3 {
  margin: 0;
}

.schedule-table__time p {
  color: #000000;
  font-size: 16px;
}

.schedule-table__event {
  padding: 35px 50px 35px 40px;
  border-top: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
  height: 100%;
  width: calc(100% - 15px);
  margin-left: -20px;
}

.schedule-table__speaker {
  padding: 40px 0 35px 40px;
  border-top: 1px solid #e6e6e6;
  width: calc(100% + 65px);
  margin-left: -65px;
}

.schedule-table__speaker__name {
  margin-left: 15px;
}

.schedule-table__speaker__name p {
  color: #333333;
  font-family: "Montserrat-600", sans-serif;
}

.schedule-table__speaker__name span {
  color: #666666;
}

.schedule-table__speaker__info {
  display: inline-flex;
  align-items: center;
  margin-top: 12px;
}

.schedule-table__speaker__info:first-child {
  margin-top: 0px;
}

.break-time {
  background-image: url("../images/break-time1.png");
  background-position: center;
  background-size: cover;
  height: 145px;
}

.break-time--lunch {
  background-image: url("../images/break-time2.png");
  background-position: center;
  background-size: cover;
}

.break-time__coffee {
  padding: 35px 0 0 20px;
}

.break-time > .row {
  height: 100%;
}

.break-time .schedule-table__time {
  border: unset;
}

.break-time + .row .schedule-table__time, .break-time + .row .schedule-table__event, .break-time + .row .schedule-table__speaker {
  border-top: unset !important;
}

@media (min-width: 1200px) {
  .schedule-table__time {
    width: 280px;
  }
  .schedule-table__greating--event {
    padding-right: 100px;
  }
}

@media (max-width: 1200px) {
  .schedule-table {
    padding-top: 60px;
  }
  .schedule-table__event {
    margin-left: -30px;
    width: calc(100% + 15px);
  }
}

@media (max-width: 992px) {
  .schedule-table__event {
    width: calc(100% + 30px);
    padding: 30px;
    border-right: unset;
  }
  .schedule-table__speaker {
    width: 100%;
    margin: 0;
    padding: 15px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    border-top: unset;
  }
  .schedule-table__speaker__info {
    margin: 15px;
  }
  .schedule-table__speaker__info:first-child {
    margin-top: 15px;
  }
  .schedule-tab h2 {
    font-size: 30px;
  }
  .schedule-tab p {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .schedule-table {
    padding-top: 40px;
  }
  .schedule-tab {
    height: 100px;
  }
  .schedule-tab h2 {
    font-size: 25px;
  }
  .schedule-tab p {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .schedule-table .row:first-child .schedule-table__time {
    padding-top: 15px;
  }
  .schedule-table__time {
    border-right: unset;
    padding: 30px 0 15px 0;
  }
  .schedule-table__event {
    width: 100%;
    padding: 0 0 15px 0;
    border: unset;
    margin: 0;
  }
  .schedule-table__speaker {
    padding: 0;
  }
  .break-time {
    height: 170px;
  }
  .break-time__coffee {
    padding: 0 0 0 15px;
    text-align: center;
  }
  .break-time__coffee h3 {
    margin: 0;
    line-height: 20px;
  }
  .schedule-tab {
    border-top: 1px solid #e6e6e6;
    flex-wrap: wrap;
    height: auto;
  }
  .schedule-tab .schedule-tab__item {
    width: 100%;
    height: 80px;
    border-left: 1px solid #e6e6e6;
    border-top: unset;
  }
}

.schedule-list__tittle h1 {
  letter-spacing: -4px;
  text-align: center;
  -webkit-text-stroke-width: 4px;
  -webkit-text-stroke-color: #000000;
  line-height: 130px;
  color: #ffffff;
  text-decoration: none;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(0deg, #000000, #000000 50%, #ffffff 50%);
  background-size: 100% 200%;
  background-position: 0% 0%;
  font-family: "Montserrat-700", sans-serif;
  font-size: 120px;
  color: #ffffff;
  margin: 135px 0 55px;
}

.schedule-list__tittle h1:hover {
  transition: all 0.8s cubic-bezier(0, 0, 0.23, 1);
  background-position: 100% 100%;
}

@media (max-width: 1200px) {
  .schedule-list__tittle h1 {
    line-height: 110px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 100px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 992px) {
  .schedule-list__tittle h1 {
    -webkit-text-stroke-width: 2px;
    line-height: 80px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 70px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 768px) {
  .schedule-list__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 65px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 55px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

@media (max-width: 576px) {
  .schedule-list__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 45px !important;
    -webkit-text-stroke-width: 1px;
    font-family: "Montserrat-700", sans-serif;
    font-size: 35px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

.schedule-list--lower {
  margin: 90px 0 0 70px;
}

.schedule-list h2 {
  text-align: center;
  margin-bottom: 35px;
}

.schedule-list__box {
  display: flex;
}

.schedule-list__box--lower {
  padding-left: 70px;
}

.schedule-list__box--lower ul {
  padding: 0 0 0 30px !important;
}

.schedule-list__box h3 {
  -webkit-writing-mode: vertical-rl;
      -ms-writing-mode: tb-rl;
          writing-mode: vertical-rl;
  /* IE 9 */
  /* Safari 3-8 */
  transform: rotate(180deg);
  font-family: "Montserrat-700", sans-serif;
  font-size: 74px;
  color: #f2f2f2;
  margin: 0;
  color: #f2f2f2;
  line-height: 70px;
  word-spacing: -3px;
  letter-spacing: -1px;
  margin-bottom: 5px;
}

.schedule-list__box ul {
  width: 100%;
  padding: 0 40px 0 30px;
}

.schedule-list__box li {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.schedule-list__box li:first-child {
  margin-top: 0px;
}

.schedule-list__box li span {
  font-size: 20px;
  color: #333333;
  font-family: "Montserrat-500", sans-serif;
}

@media screen and (max-width: 1199px) {
  .schedule-list__box ul {
    padding: 0 0 0 15px !important;
  }
  .schedule-list__box ul span {
    font-size: 18px;
  }
  .schedule-list__box--lower {
    padding: 0;
  }
}

@media screen and (max-width: 990px) {
  .schedule-list h3 {
    font-size: 70px;
  }
  .schedule-list .schedule-list--lower {
    margin: 40px 0 35px 0px;
  }
  .schedule-list__box {
    padding: 0 70px;
  }
}

@media screen and (max-width: 767px) {
  .schedule-list__box {
    padding: 0;
  }
}

@media screen and (max-width: 576px) {
  .schedule-list h3 {
    font-size: 50px;
    line-height: 50px;
  }
  .schedule-list h2 {
    font-size: 25px;
    margin-bottom: 20px;
  }
  .schedule-list__box ul li {
    margin-top: 10px;
  }
  .schedule-list__box ul span {
    font-size: 15px;
  }
}

.speaker-grid-1 {
  height: 1800px;
  width: 100%;
}

.speaker-grid-1__item {
  cursor: pointer;
  overflow: hidden;
}

.speaker-grid-1__item .detail-direction > img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -moz-filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
  /* IE 9 */
  /* Safari 3-8 */
  transform: scale(1.05);
}

.speaker-grid-1__item .detail-direction:hover img {
  -moz-filter: grayscale(0%);
  -webkit-filter: grayscale(0%);
  filter: grayscale(0%);
  /* IE 9 */
  /* Safari 3-8 */
  transform: scale(1);
}

.speaker-grid-1__item .speaker-infomation {
  display: none;
}

@media (min-width: 768px) {
  .spk--width1000 {
    width: 52.0833333%;
  }
  .spk--width460 {
    width: 23.9633333%;
  }
  .spk--width600 {
    width: 31.25%;
  }
  .spk--width400 {
    width: 20.833333%;
  }
  .spk--width520 {
    width: 27.0833333%;
  }
  .spk--width500 {
    width: 26.04166666%;
  }
  .spk--height--770 {
    height: 42.7777777%;
  }
  .spk--height--600 {
    height: 33.33333333%;
  }
  .spk--height--500 {
    height: 27.777777%;
  }
  .spk--height--530 {
    height: 29.4444444%;
  }
  .spk--height--400 {
    height: 22.222222%;
  }
  .spk--height--300 {
    height: 16.66666%;
  }
}

@media (max-width: 1500px) {
  .speaker-grid-1 {
    height: 1500px;
  }
}

@media (max-width: 1200px) {
  .speaker-grid-1 {
    height: 1200px;
  }
}

@media (max-width: 992px) {
  .speaker-grid-1 {
    height: 800px;
  }
}

@media (max-width: 767px) {
  .speaker-grid-1__item {
    width: 50%;
  }
  .spk--width1000 {
    width: 100%;
  }
}

.speaker-grid-2 {
  padding: 130px 0;
}

.speaker-gutter-sizer {
  width: 19.658%;
  position: absolute;
}

.speaker-grid-2__item {
  visibility: hidden;
  width: 40.171%;
  margin-top: 125px;
  text-align: center;
}

.speaker-grid-2__item .speaker-infomation {
  display: none;
}

.speaker-grid-2__item:nth-child(1) {
  margin-top: 0px;
}

.speaker-grid-2__item__thumb__overflow {
  overflow: hidden;
}

.speaker-grid-2__item__thumb img {
  -moz-filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
  /* IE 9 */
  /* Safari 3-8 */
  transform: scale(1.05);
}

.speaker-grid-2__item h4 {
  margin-top: 50px;
  font-size: 30px;
}

.speaker-grid-2__item .speaker-grid-2__item__thumb:hover img {
  -moz-filter: grayscale(0%);
  -webkit-filter: grayscale(0%);
  filter: grayscale(0%);
  /* IE 9 */
  /* Safari 3-8 */
  transform: scale(1);
}

@media (max-width: 992px) {
  .speaker-grid-2 {
    padding: 60px 0;
  }
  .speaker-grid-2__item {
    width: 47.5%;
    margin-top: 60px;
  }
  .speaker-grid-2__item h4 {
    margin-top: 25px;
  }
  .speaker-gutter-sizer {
    width: 5%;
  }
}

@media (max-width: 768px) {
  .speaker-grid-2 {
    padding: 40px 0;
  }
  .speaker-grid-2__item {
    margin-top: 30px;
  }
  .speaker-grid-2__item h4 {
    font-size: 20px;
    margin-top: 15px;
  }
  .speaker-grid-2__item h5 {
    font-size: 15px;
  }
}

@media (max-width: 576px) {
  .speaker-grid-2__item {
    width: 80%;
    left: 50% !important;
    /* IE 9 */
    /* Safari 3-8 */
    transform: translate(-50%, 0) !important;
  }
}

.lightbox-enabled + header {
  visibility: visible !important;
  opacity: 1 !important;
  margin-top: 0 !important;
}

.speaker-detail {
  background: #000000;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  z-index: 10;
  padding-top: 210px;
}

.speaker-detail .flickity-enabled {
  position: relative;
}

.speaker-detail .flickity-enabled:focus {
  outline: none;
}

.speaker-detail .flickity-enabled.flickity-enabled.is-draggable {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.speaker-detail .flickity-enabled .flickity-viewport {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100% !important;
}

.speaker-detail .flickity-enabled .flickity-slider {
  position: absolute;
  width: 100%;
  height: 100%;
}

.speaker-detail .flickity-enabled .flickity-slider .lightbox-container:nth-child(7) .speaker-detail__thumb img {
  -o-object-fit: contain;
     object-fit: contain;
}

.speaker-detail .flickity-enabled .is-selected .speaker-detail__info {
  -moz-filter: brightness(100%);
  -webkit-filter: brightness(100%);
  filter: brightness(100%);
}

.speaker-detail .flickity-enabled .is-selected .speaker-detail__thumb img {
  -webkit-filter: grayscale(0);
          filter: grayscale(0);
}

.speaker-detail .slider {
  width: 100%;
  height: 100%;
}

.speaker-detail .exit {
  position: absolute;
  bottom: 70px;
  left: 50%;
  /* IE 9 */
  /* Safari 3-8 */
  transform: translate(-50%, 0);
  cursor: pointer;
  display: inline-block;
  text-align: center;
}

.speaker-detail .exit .more-profile {
  overflow: hidden;
  position: absolute;
  color: transparent;
  transition: color 0s 0.3s;
  font-family: "JosefinSans-600", sans-serif;
  font-size: 25px;
  color: transparent;
  margin: -40px 0 0;
}

.speaker-detail .exit .more-profile::before {
  position: absolute;
  content: "";
  top: 7px;
  height: 20px;
  width: 100%;
  z-index: 2;
  /* IE 9 */
  /* Safari 3-8 */
  transform: translateX(-105%);
  background-color: #ffffff;
  transition: transform 0.6s cubic-bezier(0.7, 0, 0.3, 1);
}

.speaker-detail .exit .more-profile:hover {
  color: #ffffff;
}

.speaker-detail .exit .more-profile:hover::before {
  /* IE 9 */
  /* Safari 3-8 */
  transform: translateX(105%);
}

.speaker-detail .exit .explosed {
  display: inline-block;
  font-family: "JosefinSans-600", sans-serif;
  font-size: 25px;
  color: #666666;
  margin: 0;
}

.speaker-detail .exit .explosed span {
  display: inline-block;
}

.speaker-detail .prev, .speaker-detail .next {
  position: absolute;
  top: 47%;
  /* IE 9 */
  /* Safari 3-8 */
  transform: translate(0, -50%);
}

.speaker-detail .prev {
  left: 100px;
  /* IE 9 */
  /* Safari 3-8 */
  transform: rotate(-90deg);
}

.speaker-detail .next {
  right: 100px;
  /* IE 9 */
  /* Safari 3-8 */
  transform: rotate(90deg);
}

.speaker-detail__thumb {
  height: 570px;
}

.speaker-detail__thumb img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -moz-filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}

.speaker-detail__info {
  -moz-filter: brightness(0.1);
  -webkit-filter: brightness(0.1);
  filter: brightness(0.1);
  padding: 45px 0px 45px 70px;
}

.speaker-detail__info h4 {
  line-height: 60px;
  font-size: 60px;
  color: #ffffff;
}

.speaker-detail__info h5 {
  font-family: "JosefinSans-600", sans-serif;
  font-size: 30px;
  color: #999999;
  margin: 20px 0 40px;
}

.speaker-detail__info p {
  font-size: 22px;
  color: #999999;
}

.speaker-detail__info .speaker-media {
  margin: 32px 15px 0 0;
  display: inline-block;
}

.speaker-detail__info .speaker-media:hover img {
  -moz-filter: brightness(0) invert(1);
  -webkit-filter: brightness(0) invert(1);
  filter: brightness(0) invert(1);
}

.speaker-slide-arrow {
  font-size: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-align: center;
  overflow: hidden;
}

/* Mallki */
.link--mallki {
  color: #666666;
  font-family: "JosefinSans-400", sans-serif;
  font-size: 25px;
  transition: color 0.5s;
  overflow: hidden;
}

.link--mallki:hover {
  transition: none;
  color: transparent;
  font-weight: 700;
}

.link--mallki::before {
  content: '';
  width: 100%;
  height: 2px;
  margin: -3px 0 0 0;
  background: #fff;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translate3d(-102%, 0, 0);
  transition: transform 0.4s;
  transition-timing-function: cubic-bezier(0.7, 0, 0.3, 1);
}

.link--mallki:hover::before {
  transform: translate3d(101%, 0, 0);
}

.link--mallki span {
  position: absolute;
  height: 50%;
  width: 100%;
  left: 0;
  top: 0;
  overflow: hidden;
}

.link--mallki span::before {
  content: attr(data-letters);
  position: absolute;
  left: 0;
  width: 100%;
  color: #fff;
  transition: transform 0.5s;
}

.link--mallki span:nth-child(2) {
  top: 48%;
}

.link--mallki span:first-child::before {
  top: 0;
  transform: translate3d(0, 100%, 0);
}

.link--mallki span:nth-child(2)::before {
  bottom: 0;
  transform: translate3d(0, -100%, 0);
}

.link--mallki:hover span::before {
  transition-delay: 0.3s;
  transform: translate3d(0, 0, 0);
  transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}

@media (max-width: 1752px) {
  .speaker-detail {
    padding-top: 160px;
  }
  .speaker-detail .prev {
    left: 5px;
  }
  .speaker-detail .next {
    right: 5px;
  }
  .speaker-detail .exit {
    bottom: 50px;
  }
  .speaker-detail__thumb {
    width: 80%;
    height: 400px;
    margin: 0 auto;
  }
  .speaker-detail__info {
    padding-left: 0;
  }
  .speaker-detail__info h4 {
    margin-right: 5px;
    display: inline-block;
    font-size: 50px;
    line-height: 50px;
  }
  .speaker-detail__info h5 {
    font-size: 35px;
    line-height: 35px;
  }
}

@media (max-width: 1200px) {
  .speaker-detail {
    position: absolute;
    height: 800px;
  }
  .prev {
    left: 20px !important;
  }
  .next {
    right: 20px !important;
  }
  .exit {
    bottom: 50px;
  }
}

@media (max-width: 992px) {
  .speaker-detail {
    padding-top: 150px;
    height: 700px !important;
  }
  .speaker-detail .exit {
    bottom: 50px;
  }
  .speaker-detail .prev {
    left: -5px !important;
  }
  .speaker-detail .next {
    right: -5px !important;
  }
  .speaker-detail .speaker-detail__thumb {
    height: 400px;
    width: 100%;
  }
  .speaker-detail .speaker-detail__info h4 {
    margin-right: 5px;
    display: inline-block;
    font-size: 35px;
    line-height: 35px;
  }
  .speaker-detail .speaker-detail__info h5 {
    font-size: 25px;
    line-height: 25px;
    margin: 20px 0;
  }
  .speaker-detail .speaker-detail__info p {
    font-size: 15px;
  }
}

@media (max-width: 767px) {
  .speaker-detail {
    height: 1000px !important;
  }
}

@media (max-width: 576px) {
  .speaker-detail__thumb {
    height: 400px;
  }
  .speaker-detail__info {
    padding: 30px 0;
  }
}

.speaker-slider__tittle {
  text-align: center;
  margin: 100px 0 80px;
}

.speaker-slider-area {
  position: relative;
}

.speaker-slider__arrow {
  opacity: 0;
  position: absolute;
  right: 0;
  font-size: 25px;
  z-index: 1;
  transition: all 0.5s ease;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: 1.5px solid transparent;
  text-align: center;
}

.speaker-slider__arrow--up {
  top: 70px;
  line-height: 30px;
}

.speaker-slider__arrow--down {
  top: 120px;
  line-height: 32px;
}

.speaker-slider__arrow:hover {
  border: 1.5px solid #000000;
}

.speaker-slider__item {
  display: flex !important;
  width: calc(100% + 30px) !important;
  margin: 0 -15px;
}

.speaker-slider__item > div {
  opacity: 0;
}

.speaker-slider__item > div:nth-child(1) {
  padding-right: 35px;
  margin-top: 105px;
}

.speaker-slider__item > div:nth-child(3) {
  padding-left: 35px;
  margin-top: 230px;
}

.speaker-slider__item .thumb-float-down {
  -webkit-animation: FloatDown 1s;
  animation: FloatDown 1s;
}

@keyframes FloatDown {
  0% {
    /* IE 9 */
    /* Safari 3-8 */
    transform: translate(0, 0px);
  }
  100% {
    /* IE 9 */
    /* Safari 3-8 */
    transform: translate(0, 200px);
  }
}

@-webkit-keyframes FloatDown {
  0% {
    /* IE 9 */
    /* Safari 3-8 */
    transform: translate(0, 0px);
  }
  100% {
    /* IE 9 */
    /* Safari 3-8 */
    transform: translate(0, 200px);
  }
}

.speaker-slider__item__thumb {
  position: relative;
  height: 420px;
  display: block;
}

.speaker-slider__item__thumb:hover .speaker-slider__item__thumb__info {
  -webkit-clip-path: circle(100% at 50% 50%);
  clip-path: circle(100% at 50% 50%);
}

.speaker-slider__item__thumb__info {
  transition: -webkit-clip-path 1.2s;
  transition: clip-path 1.2s;
  transition: clip-path 1.2s, -webkit-clip-path 1.2s;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #ffffff;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  -webkit-clip-path: circle(0% at 50% 50%);
  clip-path: circle(0% at 50% 50%);
}

.speaker-slider__item__thumb__info h4 {
  font-size: 30px;
}

.speaker-slider__item__thumb__info h5 {
  font-size: 22px;
}

.speaker-slider__item__thumb img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -moz-filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}

@media (max-width: 1200px) {
  .speaker-slider__tittle {
    margin: 60px 0 60px;
  }
  .speaker-slider__tittle h1 {
    font-size: 70px;
  }
  .speaker-slider__item > div:nth-child(1) {
    padding-right: 15px;
    margin-top: 65px;
  }
  .speaker-slider__item > div:nth-child(3) {
    padding-left: 15px;
    margin-top: 160px;
  }
  .speaker-slider__arrow--up {
    top: 50px;
  }
  .speaker-slider__arrow--down {
    top: 100px;
  }
  .speaker-slider__item__thumb {
    height: 350px;
  }
}

@media (max-width: 991px) {
  .speaker-slider__tittle {
    margin: 40px 0 40px;
  }
  .speaker-slider__tittle h1 {
    font-size: 50px;
  }
  .speaker-slider__arrow--up {
    top: 0px;
  }
  .speaker-slider__arrow--down {
    top: 40px;
  }
  .speaker-slider .speaker-slider__item > div:nth-child(1) {
    margin-top: 0;
  }
  .speaker-slider .speaker-slider__item > div:nth-child(2) {
    margin-top: 80px;
  }
  .speaker-slider .speaker-slider__item > div:nth-child(3) {
    display: none;
  }
}

@media (max-width: 767px) {
  .speaker-slider__item > div:nth-child(1) {
    margin: 0 auto;
    width: 70%;
  }
  .speaker-slider__item > div:nth-child(2) {
    margin-top: 80px;
    display: none;
  }
}

@media (max-width: 576px) {
  .speaker-slider__tittle h1 {
    font-size: 35px;
    line-height: 35px;
  }
  .speaker-slider .speaker-slider__item > div:nth-child(1) {
    width: 320px;
  }
}

@media (max-width: 400px) {
  .speaker-slider .speaker-slider__item__thumb {
    height: 320px;
  }
  .speaker-slider .speaker-slider__item > div:nth-child(1) {
    width: 85%;
  }
}

.venue-layout__tittle h1 {
  letter-spacing: -4px;
  text-align: center;
  -webkit-text-stroke-width: 4px;
  -webkit-text-stroke-color: #000000;
  line-height: 130px;
  color: #ffffff;
  text-decoration: none;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(0deg, #000000, #000000 50%, #ffffff 50%);
  background-size: 100% 200%;
  background-position: 0% 0%;
  font-family: "Montserrat-700", sans-serif;
  font-size: 120px;
  color: #ffffff;
  margin: 160px 0 160px 0;
}

.venue-layout__tittle h1:hover {
  transition: all 0.8s cubic-bezier(0, 0, 0.23, 1);
  background-position: 100% 100%;
}

@media (max-width: 1200px) {
  .venue-layout__tittle h1 {
    line-height: 110px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 100px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 992px) {
  .venue-layout__tittle h1 {
    -webkit-text-stroke-width: 2px;
    line-height: 80px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 70px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 768px) {
  .venue-layout__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 65px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 55px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

@media (max-width: 576px) {
  .venue-layout__tittle h1 {
    letter-spacing: -2px !important;
    line-height: 45px !important;
    -webkit-text-stroke-width: 1px;
    font-family: "Montserrat-700", sans-serif;
    font-size: 35px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

.venue-fadeinup {
  opacity: 0;
}

.venue-video p {
  font-size: 22px;
}

.venue-video__iframe {
  height: 480px;
}

.venue-video__iframe video {
  width: 100%;
  height: 100%;
  -moz-filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}

.venue-video__text {
  padding-top: 30px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.venue-video__text__tittle {
  text-align: right;
}

.venue-video__text__tittle h2 {
  font-size: 50px;
}

.venue-video__text__tittle h2:nth-child(2) {
  font-family: "Montserrat-400", sans-serif;
}

.venue-video__text__address {
  margin-top: 30px;
  transition: all 0.3s ease;
}

.venue-video__text__address i {
  font-size: 70px;
  margin-right: 30px;
}

.venue-video__text__address__detail {
  display: flex;
  align-items: center;
}

.venue-video__text__address p {
  color: #000000;
  font-family: "Montserrat-600", sans-serif;
}

.venue-video__text__address:hover iframe {
  height: 230px;
}

.venue-video__text iframe {
  width: 100%;
  height: 0;
  border-width: 0px;
  transition: all 0.5s ease;
  -moz-filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}

.venue-layout-grid {
  padding-top: 150px;
}

.venue-layout-grid__item {
  width: 50%;
  margin-top: 135px;
}

.venue-layout-grid__item:nth-child(1) {
  margin-top: 0;
}

.venue-layout-grid__item__thumb {
  transition: all 0.3s ease;
  -moz-filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
  margin-bottom: 50px;
  overflow: hidden;
}

.venue-layout-grid__item h3 {
  font-family: "Montserrat-700", sans-serif;
  margin-bottom: 10px;
}

.venue-layout-grid__item__content:hover .venue-layout-grid__item__thumb {
  -moz-filter: grayscale(0%);
  -webkit-filter: grayscale(0%);
  filter: grayscale(0%);
}

.venue-layout-grid__item__content:hover .venue-layout-grid__item__thumb img {
  /* IE 9 */
  /* Safari 3-8 */
  transform: scale(1.1);
}

@media (min-width: 1752px) {
  .venue-video__iframe {
    padding-right: 92px;
    height: 650px;
  }
  .venue-video__text {
    width: calc(100% + 20px);
    margin-left: -20px;
  }
  .venue-layout-grid__item__content {
    display: block;
  }
  .venue-layout-grid__item:nth-child(1) .venue-layout-grid__item__content {
    width: 570px;
    float: right;
  }
  .venue-layout-grid__item:nth-child(2) .venue-layout-grid__item__content {
    width: 575px;
    float: right;
  }
  .venue-layout-grid__item:nth-child(3) .venue-layout-grid__item__content {
    width: 645px;
    float: left;
  }
  .venue-layout-grid__item:nth-child(4) .venue-layout-grid__item__content {
    width: 790px;
    float: left;
  }
}

@media (max-width: 1752px) {
  .venue-video p {
    font-size: 15px;
  }
  .venue-video__text__tittle h2 {
    font-size: 30px;
  }
  .venue-video__text i {
    font-size: 40px;
  }
  .venue-layout-grid {
    padding-top: 150px;
  }
  .venue-layout-grid__item__content {
    display: block;
    width: 90%;
  }
  .venue-layout-grid__item:nth-child(1) .venue-layout-grid__item__content {
    float: right;
  }
  .venue-layout-grid__item:nth-child(2) .venue-layout-grid__item__content {
    float: right;
  }
  .venue-layout-grid__item:nth-child(3) .venue-layout-grid__item__content {
    float: left;
  }
  .venue-layout-grid__item:nth-child(4) .venue-layout-grid__item__content {
    float: left;
  }
}

@media (max-width: 1200px) {
  .venue-video__iframe {
    height: 400px;
  }
  .venue-layout-grid {
    padding-top: 60px;
  }
  .venue-layout-grid__item {
    margin-top: 60px;
  }
  .venue-layout-grid__item h3 {
    font-size: 25px;
  }
  .venue-layout-grid__item__thumb {
    margin-bottom: 25px;
  }
}

@media (max-width: 992px) {
  .venue-video__iframe {
    height: 450px;
  }
}

@media (max-width: 768px) {
  .venue-layout-grid {
    padding-top: 40px;
  }
}

@media (max-width: 576px) {
  .venue-video__text h2 {
    font-size: 25px;
  }
  .venue-video__text i {
    margin-right: 20px;
  }
}

@media (max-width: 767px) {
  .venue-video__iframe {
    height: 350px;
  }
  .venue-layout-grid__item {
    margin-top: 30px;
    left: 50% !important;
    /* IE 9 */
    /* Safari 3-8 */
    transform: translate(-50%, 0);
    width: 80%;
  }
  .venue-layout-grid__item__content {
    width: 100%;
    float: unset !important;
  }
  .venue-layout-grid__item__thumb {
    margin-bottom: 10px;
  }
  .venue-layout-grid__item h3 {
    margin: 0;
    font-size: 20px;
  }
}

@media (max-width: 540px) {
  .venue-video__iframe {
    height: 310px;
  }
}

@media (max-width: 540px) and (max-width: 435px) {
  .venue-video__iframe {
    height: 250px;
  }
}

@media (max-width: 540px) and (max-width: 400px) {
  .venue-video__iframe {
    height: 230px;
  }
}

@media (max-width: 540px) and (max-width: 375px) {
  .venue-video__iframe {
    height: 200px;
  }
}

.experience__tittle {
  overflow: hidden;
  padding: 130px 0 130px;
}

.experience__tittle .experience__tittle1 h1 {
  letter-spacing: -4px;
  text-align: left;
  -webkit-text-stroke-width: 4px;
  -webkit-text-stroke-color: #000000;
  line-height: 130px;
  color: #ffffff;
  text-decoration: none;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(0deg, #000000, #000000 50%, #ffffff 50%);
  background-size: 100% 200%;
  background-position: 0% 0%;
  font-family: "Montserrat-700", sans-serif;
  font-size: 120px;
  color: #ffffff;
  margin: 0;
}

.experience__tittle .experience__tittle1 h1:hover {
  transition: all 0.8s cubic-bezier(0, 0, 0.23, 1);
  background-position: 100% 100%;
}

@media (max-width: 1200px) {
  .experience__tittle .experience__tittle1 h1 {
    line-height: 110px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 100px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 992px) {
  .experience__tittle .experience__tittle1 h1 {
    -webkit-text-stroke-width: 2px;
    line-height: 80px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 70px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 768px) {
  .experience__tittle .experience__tittle1 h1 {
    letter-spacing: -2px !important;
    line-height: 65px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 55px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

@media (max-width: 576px) {
  .experience__tittle .experience__tittle1 h1 {
    letter-spacing: -2px !important;
    line-height: 45px !important;
    -webkit-text-stroke-width: 1px;
    font-family: "Montserrat-700", sans-serif;
    font-size: 35px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

.experience__tittle .experience__tittle2 h1 {
  letter-spacing: -4px;
  text-align: right;
  -webkit-text-stroke-width: 4px;
  -webkit-text-stroke-color: #000000;
  line-height: 130px;
  color: #ffffff;
  text-decoration: none;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(0deg, #000000, #000000 50%, #ffffff 50%);
  background-size: 100% 200%;
  background-position: 0% 0%;
  font-family: "Montserrat-700", sans-serif;
  font-size: 120px;
  color: #ffffff;
  margin: 0;
}

.experience__tittle .experience__tittle2 h1:hover {
  transition: all 0.8s cubic-bezier(0, 0, 0.23, 1);
  background-position: 100% 100%;
}

@media (max-width: 1200px) {
  .experience__tittle .experience__tittle2 h1 {
    line-height: 110px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 100px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 992px) {
  .experience__tittle .experience__tittle2 h1 {
    -webkit-text-stroke-width: 2px;
    line-height: 80px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 70px;
    color: #ffffff;
    margin: 60px 0 !important;
  }
}

@media (max-width: 768px) {
  .experience__tittle .experience__tittle2 h1 {
    letter-spacing: -2px !important;
    line-height: 65px !important;
    font-family: "Montserrat-700", sans-serif;
    font-size: 55px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

@media (max-width: 576px) {
  .experience__tittle .experience__tittle2 h1 {
    letter-spacing: -2px !important;
    line-height: 45px !important;
    -webkit-text-stroke-width: 1px;
    font-family: "Montserrat-700", sans-serif;
    font-size: 35px;
    color: #ffffff;
    margin: 40px 0 !important;
  }
}

.experience__tittle h1 {
  display: inline-block;
}

.experience__tittle h3 {
  text-align: center;
  font-family: "Montserrat-700", sans-serif;
  font-size: 170px;
  color: #000000;
  margin: 0;
  line-height: 285px;
}

.experience__tittle .experience__tittle1 h1 {
  -webkit-animation: scroll-left 10s linear infinite;
  animation: scroll-left 10s linear infinite;
}

.experience__tittle .experience__tittle2 h1 {
  -webkit-animation: scroll-right 10s linear infinite;
  animation: scroll-right 10s linear infinite;
}

.experience .row:nth-child(1) {
  padding: 0 275px;
}

.experience .row:nth-child(2) {
  margin-top: -40px;
}

.experience .row:nth-child(3) > div {
  margin: 0 auto;
}

.experience__thumb {
  display: block;
  overflow: hidden;
}

.experience__thumb img {
  -moz-filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}

.experience__thumb:hover img {
  -moz-filter: grayscale(0%);
  -webkit-filter: grayscale(0%);
  filter: grayscale(0%);
  /* IE 9 */
  /* Safari 3-8 */
  transform: scale(1.05);
}

.experience__thumb--left {
  float: left;
}

.experience__thumb--right {
  float: right;
}

.experience__thumb--middle {
  float: right;
  margin-top: 100px;
}

.experience__text {
  text-align: center;
  width: 77%;
  margin: 85px auto 0;
}

.experience__text p {
  font-family: "Montserrat-400", sans-serif;
  font-size: 22px;
  color: #333333;
  margin: 25px 0 40px;
}

.experience__text a {
  position: relative;
  color: #000000;
  text-transform: uppercase;
  font-family: "JosefinSans-600", sans-serif;
  display: inline-block;
}

.experience__text a::before {
  bottom: 3px;
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  border-bottom: 1px solid #a3a3a3;
  border-top: 1px solid #c8c8c8;
}

.experience__text .link-draw__hover {
  display: inline-block;
  position: relative;
}

@media (max-width: 991px) {
  .experience__text .link-draw__hover svg {
    display: none;
  }
}

.experience__text .link-draw__hover:hover path {
  stroke: #000000;
  -webkit-animation: draw 1s linear alternate forwards;
  animation: draw 1s linear alternate forwards;
}

.experience__text .link-draw__hover svg {
  position: absolute;
  width: 180px;
  top: -60px;
  left: -30px;
}

.experience__text .link-draw__hover svg path {
  -webkit-animation: drawReverse 0.5s;
  animation: drawReverse 0.5s;
  fill: none;
  stroke: transparent;
  transition: stroke 1s;
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
}

@keyframes draw {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@-webkit-keyframes draw {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes drawReverse {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 1000;
  }
}

@-webkit-keyframes drawReverse {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 1000;
  }
}

#counting .letter {
  display: inline-block;
}

.movein {
  transform: translate(50%, 50%);
}

@media (max-width: 1752px) {
  @keyframes scroll-left {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(140%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
  }
  @-webkit-keyframes scroll-left {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(140%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
  }
  @keyframes scroll-right {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(140%);
    }
  }
  @-webkit-keyframes scroll-right {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(140%);
    }
  }
  .experience .row:nth-child(1) {
    padding: 0 70px;
  }
  .experience__text {
    margin: 55px auto 40px;
  }
  .experience__text h1 {
    font-size: 60px;
  }
  .experience__text p {
    margin: 20px 0;
    font-size: 20px;
  }
  .experience__thumb--left, .experience__thumb--middle {
    width: calc(100% + 35px);
  }
  .experience__thumb--right img {
    max-height: 400px;
  }
}

@media (min-width: 1752px) {
  @keyframes scroll-left {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(180%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
  }
  @-webkit-keyframes scroll-left {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(180%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
  }
  @keyframes scroll-right {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(180%);
    }
  }
  @-webkit-keyframes scroll-right {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(180%);
    }
  }
}

@media (max-width: 1200px) {
  .experience__tittle {
    padding: 60px 0;
  }
  .experience__tittle h3 {
    font-size: 100px !important;
    line-height: 70px !important;
  }
}

@media (max-width: 1199px) {
  @keyframes scroll-left {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(100%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
  }
  @-webkit-keyframes scroll-left {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(100%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
  }
  @keyframes scroll-right {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(100%);
    }
  }
  @-webkit-keyframes scroll-right {
    0% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(-100%);
    }
    100% {
      /* IE 9 */
      /* Safari 3-8 */
      transform: translateX(100%);
    }
  }
}

@media (max-width: 992px) {
  .experience__tittle {
    padding: 0;
  }
  .experience__tittle h3 {
    font-size: 80px !important;
    line-height: 60px !important;
  }
}

@media (max-width: 768px) {
  .experience__tittle h3 {
    font-size: 60px !important;
    line-height: 40px !important;
  }
}

@media (max-width: 576px) {
  .experience__tittle h3 {
    font-size: 40px !important;
    line-height: 30px !important;
  }
  .experience__tittle .experience__tittle1 h1 {
    margin-bottom: 20px !important;
  }
  .experience__tittle .experience__tittle2 h1 {
    margin-top: 20px !important;
  }
}

@media screen and (max-width: 1199px) {
  .experience__thumb--left, .experience__thumb--middle {
    width: 100%;
  }
  .experience__text {
    width: 100%;
  }
  .experience__text h1 {
    font-size: 40px;
  }
  .experience__text p {
    margin: 20px 0;
    font-size: 18px;
  }
  .experience__thumb--right img {
    max-height: 330px;
  }
  .experience .row:nth-child(3) > div .experience__thumb--center {
    text-align: center;
    margin-top: 35px;
  }
  .experience .row:nth-child(3) > div .experience__thumb--center img {
    max-height: 300px;
  }
  .experience .row:nth-child(2) {
    margin-top: -60px;
  }
}

@media (max-width: 992px) {
  .experience .row:nth-child(1) {
    padding: 0px;
  }
  .experience .row:nth-child(1) .experience__thumb img {
    height: 250px;
    width: 100%;
    -o-object-fit: cover;
       object-fit: cover;
  }
  .experience .row:nth-child(2) {
    margin-top: 30px;
  }
  .experience .row:nth-child(2) .experience__thumb--middle {
    margin: 0;
  }
  .experience .row:nth-child(2) .experience__thumb img {
    height: 250px;
    width: 100%;
    -o-object-fit: cover;
       object-fit: cover;
  }
  .experience .row:nth-child(2) > div:nth-child(2) {
    order: 1;
  }
  .experience .row:nth-child(2) .experience__text {
    margin: 40px 0;
  }
  .experience .row:nth-child(3) > div {
    margin: 0 auto;
  }
  .experience .row:nth-child(3) .experience__thumb--center {
    margin-top: 0 !important;
  }
  .experience__text {
    opacity: 1;
  }
}

@media (max-width: 576px) {
  .experience__thumb {
    margin-top: 30px !important;
  }
  .experience__thumb--right {
    width: 100%;
  }
  .experience .row {
    margin-top: 0 !important;
  }
  .experience .row:nth-child(1) > div:first-child .experience__thumb {
    margin-top: 0px !important;
  }
  .experience__text h1 {
    font-size: 30px;
  }
  .experience__text p {
    font-size: 15px;
  }
}

.erropage {
  background-color: #000000;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.erropage .body {
  font-family: 'Acme';
  margin-top: 300px;
}

.erropage .body::before {
  font-weight: 400;
  font-size: 150px;
  line-height: 100px;
  margin-bottom: 35px;
}

.erropage .body p {
  font-size: 28px;
  color: #ffffff;
  font-family: "JosefinSans-400", sans-serif;
}

.erropage .backBtn {
  margin-top: 20px;
  color: #000000;
  font-family: "JosefinSans-600", sans-serif;
  display: inline-block;
  text-align: center;
  line-height: 47px;
  background-color: #ffffff;
  border: 1.5px solid #ffffff;
  height: 45px;
  width: 130px;
  text-transform: uppercase;
  background-image: linear-gradient(to right, #000000, #000000 50%, #ffffff 50%);
  background-size: 210% 100%;
  background-position: 99%;
}

.erropage .backBtn:hover {
  transition: all 0.5s cubic-bezier(0, 0, 0.23, 1);
  background-position: 0%;
  color: #ffffff;
}

.erropage-area {
  padding-top: 60px;
  text-align: center;
  position: absolute;
  top: 45%;
  width: 100%;
  /* IE 9 */
  /* Safari 3-8 */
  transform: translate(0, -50%);
}

.head {
  display: block;
  position: relative;
  width: 400px;
  margin-left: auto;
  margin-right: auto;
  -webkit-animation: shvr 0.2s infinite;
  animation: shvr 0.2s infinite;
}

.head::after {
  content: '';
  width: 35px;
  height: 35px;
  background: #000;
  position: absolute;
  top: 60px;
  left: 70px;
  border-radius: 50%;
  -ms-box-shadow: 200px 0 0 #000;
  box-shadow: 200px 0 0 #000;
  -o-box-shadow: 200px 0 0 #000;
  -webkit-animation: eye 2.5s infinite;
  animation: eye 2.5s infinite;
}

.meta {
  float: left;
  position: relative;
  display: block;
  background: #fff;
  width: 160px;
  height: 170px;
  border-radius: 63% 37% 58% 42% / 49% 36% 64% 51%;
}

.meta::after {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 15px;
  border-radius: 63% 37% 58% 42% / 49% 36% 64% 51%;
  border-bottom: 5px solid #ffffff;
}

.meta::before {
  content: '';
  width: 70%;
  height: 100%;
  bottom: 180px;
  left: -20px;
  position: absolute;
  /* IE 9 */
  /* Safari 3-8 */
  transform: rotate(-20deg);
  border-radius: 0% 100% 44% 56% / 92% 86% 14% 8%;
  border-bottom: 10px solid #ffffff;
}

.meta:nth-of-type(2) {
  float: right;
  transform: scaleX(-1);
}

.meta:nth-of-type(2)::after {
  left: 5px;
}

.meta:nth-of-type(3) {
  display: none;
}

.body {
  margin-top: 100px;
  text-align: center;
  color: #fff;
}

.body::before {
  content: '404';
  font-size: 80px;
  font-weight: 800;
  display: block;
  margin-bottom: 10px;
}

@keyframes eye {
  0%, 30%, 55%, 90%, 100% {
    transform: translate(0, 0);
  }
  10%, 25% {
    transform: translate(0, 20px);
  }
  65% {
    transform: translate(-20px, 0);
  }
  80% {
    transform: translate(20px, 0);
  }
}

@-webkit-keyframes eye {
  0%, 30%, 55%, 90%, 100% {
    transform: translate(0, 0);
  }
  10%, 25% {
    transform: translate(0, 20px);
  }
  65% {
    transform: translate(-20px, 0);
  }
  80% {
    transform: translate(20px, 0);
  }
}

@keyframes shvr {
  0% {
    transform: translate(1px, 1em);
  }
  50% {
    transform: translate(0, 1em);
  }
  100% {
    transform: translate(-1px, 1em);
  }
}

@-webkit-keyframes shvr {
  0% {
    transform: translate(1px, 1em);
  }
  50% {
    transform: translate(0, 1em);
  }
  100% {
    transform: translate(-1px, 1em);
  }
}

@media (max-width: 1200px) {
  .erropage-area {
    top: 40%;
  }
}

@media (max-width: 576px) {
  .body {
    margin-top: 200px !important;
  }
  .body::before {
    margin-bottom: 10px !important;
    font-size: 100px !important;
  }
  .body p {
    font-size: 22px !important;
  }
  .backBtn {
    margin-top: 0 !important;
  }
  .meta {
    width: 120px;
    height: 130px;
  }
  .meta::before {
    bottom: 140px;
  }
  .head {
    width: 290px;
  }
  .head::after {
    left: 50px;
    top: 40px;
    -ms-box-shadow: 155px 0 0 #000;
    box-shadow: 155px 0 0 #000;
    -o-box-shadow: 155px 0 0 #000;
  }
}

.loading {
  background-color: #ffffff;
  position: fixed;
  z-index: 9999;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

/* Spinner 3Balls Scale */
.sp-3balls, .sp-3balls:before, .sp-3balls:after {
  border-radius: 50%;
  background-color: #000000;
  width: 18px;
  height: 18px;
  transform-origin: center center;
  display: inline-block;
}

.sp-3balls {
  position: relative;
  left: 50%;
  top: 50%;
  /* IE 9 */
  /* Safari 3-8 */
  transform: translate(-50%, -50%);
  background-color: #000000;
  opacity: 1;
  -webkit-animation: spScaleAlpha 1s infinite linear;
  animation: spScaleAlpha 1s infinite linear;
}

.sp-3balls:before, .sp-3balls:after {
  content: '';
  position: relative;
  opacity: 0.25;
}

.sp-3balls:before {
  left: 30px;
  top: 0px;
  -webkit-animation: spScaleAlphaBefore 1s infinite linear;
  animation: spScaleAlphaBefore 1s infinite linear;
}

.sp-3balls:after {
  left: -30px;
  top: -24px;
  -webkit-animation: spScaleAlphaAfter 1s infinite linear;
  animation: spScaleAlphaAfter 1s infinite linear;
}

@-webkit-keyframes spScaleAlpha {
  0% {
    opacity: 1;
  }
  33% {
    opacity: 0.25;
  }
  66% {
    opacity: 0.25;
  }
  100% {
    opacity: 1;
  }
}

@keyframes spScaleAlpha {
  0% {
    opacity: 1;
  }
  33% {
    opacity: 0.25;
  }
  66% {
    opacity: 0.25;
  }
  100% {
    opacity: 1;
  }
}

@-webkit-keyframes spScaleAlphaBefore {
  0% {
    opacity: 0.25;
  }
  33% {
    opacity: 1;
  }
  66% {
    opacity: 0.25;
  }
}

@keyframes spScaleAlphaBefore {
  0% {
    opacity: 0.25;
  }
  33% {
    opacity: 1;
  }
  66% {
    opacity: 0.25;
  }
}

@-webkit-keyframes spScaleAlphaAfter {
  33% {
    opacity: 0.25;
  }
  66% {
    opacity: 1;
  }
  100% {
    opacity: 0.25;
  }
}

@keyframes spScaleAlphaAfter {
  33% {
    opacity: 0.25;
  }
  66% {
    opacity: 1;
  }
  100% {
    opacity: 0.25;
  }
}

/*--------------------------------------------------------------
HEADER
--------------------------------------------------------------*/
.menu-nav {
  transition: all 0.3s;
  position: fixed;
  overflow-y: scroll;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  /* IE 9 */
  /* Safari 3-8 */
  transform: translateX(-100%);
  background-color: rgba(0, 0, 0, 0.95);
  /* General link styles */
  /* Kukuri */
}

.menu-nav .header__logo {
  position: absolute;
  left: 50%;
  /* IE 9 */
  /* Safari 3-8 */
  transform: translate(-50%, 0);
  margin-left: 0;
  top: 40px;
}

.menu-nav nav {
  position: absolute;
  left: 50%;
  top: 150px;
  /* IE 9 */
  /* Safari 3-8 */
  transform: translate(-50%, 0%);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0;
}

.menu-nav nav ul {
  width: 350px;
  margin: 0;
}

.menu-nav .menu-nav-close {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

.menu-nav li {
  list-style-type: none;
  position: relative;
  text-align: center;
  width: 100%;
}

.menu-nav li .sub-menu {
  display: none;
}

.menu-nav li .sub-menu a {
  font-size: 35px;
}

.menu-nav li .sub-menu > li .sub-menu {
  display: none;
}

.menu-nav li .sub-menu > li i {
  top: 15px;
  right: 10px;
}

.menu-nav li i {
  position: absolute;
  transition: all 0.3s;
  top: 25px;
  right: 0;
  color: #ffffff;
}

.menu-nav li i::before {
  font-size: 25px;
}

.menu-nav li .arrow-rotation {
  /* IE 9 */
  /* Safari 3-8 */
  transform: rotate(180deg);
}

.menu-nav .menu-link {
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #ffffff;
  font-family: "JosefinSans-600", sans-serif;
  overflow: hidden;
  outline: none;
  text-decoration: none;
  position: relative;
  font-size: 50px;
  color: transparent;
  display: inline-block;
}

.menu-nav .link--kukuri {
  transition: margin 0.5s;
}

.menu-nav .link--kukuri::after {
  content: '';
  position: absolute;
  height: 5px;
  width: 100%;
  top: 50%;
  margin-top: -3px;
  right: 0;
  background: #ffffff;
  transform: translate3d(-101%, 0, 0);
  transition: transform 0.5s;
  transition-timing-function: cubic-bezier(0.7, 0, 0.3, 1);
}

.menu-nav .link--kukuri::before {
  content: attr(data-letters);
  position: absolute;
  overflow: hidden;
  color: #ffffff;
  white-space: nowrap;
  width: 0%;
  transition: width 0.4s 0.3s;
}

.menu-nav .link--kukuri:hover::after {
  transform: translate3d(101%, 0, 0);
}

.menu-nav .link--kukuri:hover::before {
  width: 100%;
}

.menu-nav-active {
  /* IE 9 */
  /* Safari 3-8 */
  transform: translateX(0);
}

.button-eff {
  position: fixed !important;
  z-index: 100;
}

.button-eff span {
  -ms-box-shadow: 0px 0px 0px 0px #ffffff !important;
  box-shadow: 0px 0px 0px 0px #ffffff !important;
  -o-box-shadow: 0px 0px 0px 0px #ffffff !important;
}

.button-eff::before {
  width: 15px !important;
  height: 15px !important;
}

.button-eff + .position-fixed {
  display: block;
}

.position-fixed {
  display: none;
  width: 23px;
}

@media (max-width: 576px) {
  .menu-link {
    font-size: 40px !important;
  }
  .sub-menu a {
    font-size: 30px !important;
  }
  .menu-nav {
    padding-top: 100px;
  }
  .menu-nav ul {
    width: 270px !important;
  }
}

header {
  position: fixed;
  background-color: #000000;
  height: 100px;
  width: 100%;
  z-index: 100;
  transition: all 0.5s;
}

.header-hide {
  visibility: hidden;
  transition: all 0.8s;
  opacity: 0;
  margin-top: -100px;
}

.header-posistion {
  height: 100px;
  background-color: #000000;
}

.header, .header-home {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100px;
}

.header__logo {
  margin-left: 100px;
}

.header__buy-ticket {
  color: #000000;
  font-family: "JosefinSans-600", sans-serif;
  display: inline-block;
  text-align: center;
  line-height: 47px;
  background-color: #ffffff;
  border: 1.5px solid #ffffff;
  height: 45px;
  width: 130px;
  text-transform: uppercase;
  background-image: linear-gradient(to right, #000000, #000000 50%, #ffffff 50%);
  background-size: 210% 100%;
  background-position: 99%;
}

.header__buy-ticket:hover {
  transition: all 0.5s cubic-bezier(0, 0, 0.23, 1);
  background-position: 0%;
  color: #ffffff;
}

.header__menu-icon {
  position: relative;
  width: 23px;
  height: 23px;
}

.header__menu-icon::before {
  content: "";
  background-color: #ffffff;
  border-radius: 50%;
  position: absolute;
  transition: 0.8s;
  width: 0;
  height: 0;
  left: 50%;
  top: 50%;
  /* IE 9 */
  /* Safari 3-8 */
  transform: translate(-50%, -50%);
}

.header__menu-icon__dot {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
}

.header__menu-icon__dot span {
  left: 8px;
  top: 8px;
  transition: 0.5s ease-out;
  position: absolute;
  display: block;
  width: 7px;
  height: 7px;
  background-color: #ffffff;
  border-radius: 50%;
  -ms-box-shadow: -9px 0px 0px 0px #ffffff;
  box-shadow: -9px 0px 0px 0px #ffffff;
  -o-box-shadow: -9px 0px 0px 0px #ffffff;
}

.header__menu-icon__dot span:nth-child(2) {
  -ms-box-shadow: 9px 0px 0px 0px #ffffff;
  box-shadow: 9px 0px 0px 0px #ffffff;
  -o-box-shadow: 9px 0px 0px 0px #ffffff;
}

.header__menu-icon__dot--vertical {
  /* IE 9 */
  /* Safari 3-8 */
  transform: rotate(90deg);
}

.header__menu-icon__dot--skew, .header__menu-icon__dot--skew-reverse {
  /* IE 9 */
  /* Safari 3-8 */
  transform: rotate(45deg);
}

.header__menu-icon__dot--skew span, .header__menu-icon__dot--skew-reverse span {
  -ms-box-shadow: -12.6px 0px 0px 0px #ffffff;
  box-shadow: -12.6px 0px 0px 0px #ffffff;
  -o-box-shadow: -12.6px 0px 0px 0px #ffffff;
}

.header__menu-icon__dot--skew span:nth-child(2), .header__menu-icon__dot--skew-reverse span:nth-child(2) {
  -ms-box-shadow: 12.6px 0px 0px 0px #ffffff;
  box-shadow: 12.6px 0px 0px 0px #ffffff;
  -o-box-shadow: 12.6px 0px 0px 0px #ffffff;
}

.header__menu-icon__dot--skew-reverse {
  /* IE 9 */
  /* Safari 3-8 */
  transform: rotate(-45deg);
}

.header__menu-icon:hover::before {
  width: 15px;
  height: 15px;
}

.header__menu-icon:hover span {
  -ms-box-shadow: 0px 0px 0px 0px #ffffff;
  box-shadow: 0px 0px 0px 0px #ffffff;
  -o-box-shadow: 0px 0px 0px 0px #ffffff;
}

.header-home .header__buy-ticket {
  color: #ffffff;
  font-family: "JosefinSans-600", sans-serif;
  display: inline-block;
  text-align: center;
  line-height: 50px;
  background-color: transparent;
  border: 1.5px solid #ffffff;
  height: 45px;
  width: 130px;
  text-transform: uppercase;
  background-image: linear-gradient(to right, #ffffff, #ffffff 50%, #000000 50%);
  background-size: 210% 100%;
  background-position: 100%;
}

.header-home .header__buy-ticket:hover {
  transition: all 0.5s cubic-bezier(0, 0, 0.23, 1);
  background-position: 0%;
}

.header-home .header__buy-ticket:hover span {
  color: #000000;
}

@media screen and (max-width: 767px) {
  .header__logo {
    margin-left: 0;
  }
  .header__buy-ticket {
    width: 100px !important;
    font-size: 13px;
    height: 40px !important;
    line-height: 42px !important;
  }
}

/*--------------------------------------------------------------
FOOTER
--------------------------------------------------------------*/
footer {
  background-color: #000000;
}

.footer {
  padding: 110px 0 100px;
  text-align: center;
}

.footer__logo img {
  opacity: 0;
  margin-bottom: 30px;
}

.footer p {
  color: #ffffff;
}

.footer__site-text p {
  font-size: 18px;
}

.footer__get-ticket {
  text-align: center;
}

.footer__get-ticket a {
  color: #000000;
  font-family: "JosefinSans-600", sans-serif;
  display: inline-block;
  text-align: center;
  line-height: 47px;
  background-color: #ffffff;
  border: 1.5px solid #ffffff;
  height: 45px;
  width: 130px;
  text-transform: uppercase;
  background-image: linear-gradient(to right, #000000, #000000 50%, #ffffff 50%);
  background-size: 210% 100%;
  background-position: 99%;
  margin: 50px 0;
}

.footer__get-ticket a:hover {
  transition: all 0.5s cubic-bezier(0, 0, 0.23, 1);
  background-position: 0%;
  color: #ffffff;
}

.footer__menu {
  color: #ffffff;
  text-align: center;
}

.footer__menu li {
  list-style-type: none;
  display: inline-block;
  margin: 0 30px;
}

.footer__menu a {
  color: #999999;
}

.footer__menu a:hover {
  color: #ffffff;
}

.copy-right {
  border-top: 1px solid #666666;
}

.copy-right__area {
  height: 77px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copy-right__left {
  color: #999999;
}

.copy-right__left span {
  color: #ffffff !important;
}

.copy-right__right {
  width: 150px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copy-right__right img:hover {
  -webkit-filter: brightness(0) invert(1);
  filter: brightness(0) invert(1);
}

@media screen and (max-width: 767px) {
  .footer {
    padding: 80px 0 60px;
  }
  .footer__menu li {
    display: block;
    margin: 0 0 5px 0;
  }
  .copy-right .copy-right__area {
    text-align: center;
    height: 100px;
    padding: 5px 0;
    flex-wrap: wrap;
    justify-content: center;
  }
}

/*--------------------------------------------------------------
REPONSIVE
--------------------------------------------------------------*/

/*# sourceMappingURL=maps/style.css.map */
