.text-blue {
    color: #0A0AFD !important;
}

.header__logo {
    margin-left: 0;
}

.header__logo img {
    width: 500px
}

.alert.alert-success.text-center {
    background-color: #0A0AFD;
    color: #fff;
    font-size: 14px;
    letter-spacing: 1px;
    border-color: #0A0AFD;
    padding: 10px 18px;
    margin-bottom: 20px;
    border-radius: 3px;
    font-family: "Montserrat", sans-serif;
}

.video-wrapper {
    height: 56vw;
    position: relative;
    overflow: hidden;
    top: -1px;
    background-color: #343a40;
}

.video-wrapper>iframe {
    height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.pagebanner-1__tittle {
    margin-left: 12px;
    width: 400px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}



/* .pagebanner-1__tittle {
    margin-left: 0;
    width: 400px;
} */

.pagebanner-1__tittle__webdesign {
    font-size: 60px;
    font-family: "Montserrat", sans-serif;
}

.pagebanner-1__tittle .letter {
    margin-right: 3px;
}

.banner-btn {
    background-image: linear-gradient(to right, #ffffff, #ffffff 50%, #00000000 50%) !important;
    min-width: 130px !important;
    width: auto !important;
    background-color: transparent !important;
    color: #fff !important;
    padding: 0 20px !important;
}

.banner-btn:hover {
    color: #000 !important;
}

.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: .475rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.btn-white-outlined {
    font: bold 14px Oswald;
    text-transform: uppercase;
    background-color: transparent;
    border: 2px solid #fff;
    border-radius: 0;
    color: #fff;
    width: 100%;
    height: 100%;
    white-space: normal;
}

.btn-white-outlined:hover,
.btn-white-outlined:active,
.btn-white-outlined:focus,
.btn-white-outlined:hover p {
    background-color: #fff;
    color: #161616;
    outline: none;
    box-shadow: none;
}

.btn-white-outlined p {
    color: #fff;
    font-size: 12px;
}

.btn-white-outlined p:hover {
    color: #161616;
}

.footer__logo .footer-move-up {
    width: 400px;
}

.sponsor li {
    margin-top: 20px;
    justify-content: space-between;
}

.sponsor li span {
    font-size: 18px;
    color: #333333;
    font-family: "Montserrat-500", sans-serif;
}

.btn-sponsor {
    transition: all 0.3s;
    font-family: "JosefinSans-600", sans-serif;
    letter-spacing: -1px;
    text-decoration: underline;
    height: 18px;
}

.schedule-list__box li span {
    font-size: 18px;
}

.sponsor-draw-hover svg {
    position: absolute !important;
    width: 220px !important;
    top: -80px !important;
    left: -44px !important;
}

@media (min-width: 767px) {
    .float-md-right {
        float: right;
    }
}

@media (max-width: 767px) {
    .header__logo img {
        width: 50vw;
    }

    .pagebanner-1 {
        height: 100vw;
    }

    .pagebanner-1__tittle__webdesign {
        font-size: 10vw;
        line-height: 13vw;
    }

    .pagebanner-1__tittle .letter {
        margin-right: 1vw;
    }




}

@media (max-width: 575px) {
    .header__logo img {
        width: 60vw;
    }

    .video-wrapper {
        height: 100vw;
    }

    .schedule-list__box li span {
        font-size: 16px;
    }

    body,
    button,
    input,
    select,
    textarea {
        font-size: 0.8rem;
    }

}